﻿using Microsoft.Azure;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Tooling.Connector;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_BatchJobs.Infrastructure
{
    public class CRMConnection
    {
        public static IOrganizationService GetCrmConnection()
        {
            try
            {
                //Logger.Info("Inside GetCrmConnection method");

                //string clientId = CloudConfigurationManager.GetSetting("ClientId") == String.Empty ? ConfigurationManager.AppSettings["ClientId"] : CloudConfigurationManager.GetSetting("ClientId");
                //string clientSecret = CloudConfigurationManager.GetSetting("ClientSecret") == String.Empty ? ConfigurationManager.AppSettings["ClientSecret"] : CloudConfigurationManager.GetSetting("ClientSecret");
                //string resourceUrl = CloudConfigurationManager.GetSetting("Resource") == String.Empty ? ConfigurationManager.AppSettings["Resource"] : CloudConfigurationManager.GetSetting("Resource");

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                //Logger.Verbose("Connecting to CRM Organization Service started.");

                string clientId = CloudConfigurationManager.GetSetting("ClientId") == String.Empty ? ConfigurationManager.AppSettings["ClientId"] : CloudConfigurationManager.GetSetting("ClientId");
                string clientSecret = CloudConfigurationManager.GetSetting("ClientSecret") == String.Empty ? ConfigurationManager.AppSettings["ClientSecret"] : CloudConfigurationManager.GetSetting("ClientSecret");
                string resourceUrl = CloudConfigurationManager.GetSetting("Resource") == String.Empty ? ConfigurationManager.AppSettings["Resource"] : CloudConfigurationManager.GetSetting("Resource");


                string connectionString = $@"AuthType=ClientSecret;url={resourceUrl};ClientId={clientId};ClientSecret={clientSecret}";

                //string connectionString = $@"AuthType=ClientSecret;url={resourceUrl};ClientId={clientId};ClientSecret={clientSecret}";
                var delay = TimeSpan.FromSeconds(10); //Set Wait timer between retry connections
                int retryCounter = 5; //Set how many times retry to CRM to work in case of connection issue.
                var attempts = 0;

                do
                {
                    attempts++;

                    CrmServiceClient crmServiceClient = new CrmServiceClient(connectionString);

                    if (crmServiceClient.IsReady)
                    {
                        //Setting Timeout to 15 Mins
                        crmServiceClient.OrganizationWebProxyClient.InnerChannel.OperationTimeout = new TimeSpan(0, 15, 0);

                        //Logger.Info("Established connection with CRM Organization service.");

                        return crmServiceClient.OrganizationWebProxyClient != null ? crmServiceClient.OrganizationWebProxyClient : (IOrganizationService)crmServiceClient.OrganizationServiceProxy;

                    }
                    else
                    {

                        //Logger.Verbose("Failed connecting to: " + resourceUrl);
                        //Logger.Error(crmServiceClient.LastCrmError);

                        //Try Again
                        if (attempts == retryCounter)
                            throw crmServiceClient.LastCrmException;

                        //Logger.Error($"Exception caught on attempt {attempts} - will retry after delay {delay.TotalSeconds} Seconds", crmServiceClient.LastCrmException);

                        Task.Delay(delay).Wait();

                    }

                } while (true);
            }
            catch (Exception ex)
            {
                //Logger.Error("Error while connecting to CRM", ex);
                throw;
            }

        }
    }
}
