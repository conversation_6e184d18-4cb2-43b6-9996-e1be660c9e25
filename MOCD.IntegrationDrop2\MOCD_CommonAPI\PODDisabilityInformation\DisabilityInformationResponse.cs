﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace MOCD_CommonAPI.PODDisabilityInformation
{
    [XmlRoot(ElementName = "PODCardModels.MultipleDisabilityDetail", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
    public class MultipleDisabilityDetail
    {

        [XmlElement(ElementName = "TypeTitleAr", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public string TypeTitleAr;

        [XmlElement(ElementName = "TypeTitleEn", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public string TypeTitleEn;
    }

    [XmlRoot(ElementName = "MultipleDisabilityDetails", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
    public class MultipleDisabilityDetails
    {

        [XmlElement(ElementName = "PODCardModels.MultipleDisabilityDetail", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public List<MultipleDisabilityDetail> multipleDisabilityDetail;
    }

    [XmlRoot(ElementName = "Content", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
    public class Content
    {

        [XmlElement(ElementName = "LevelTitleAr", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public string LevelTitleAr;

        [XmlElement(ElementName = "LevelTitleEn", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public string LevelTitleEn;

        [XmlElement(ElementName = "MultipleDisabilityDetails", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public MultipleDisabilityDetails MultipleDisabilityDetails;

        [XmlElement(ElementName = "TypeTitleAr", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public string TypeTitleAr;

        [XmlElement(ElementName = "TypeTitleEn", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public string TypeTitleEn;
    }

    [XmlRoot(ElementName = "MOCDResponseOfPODCardModels.DisabilityInformation4KQv0V5c", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
    public class DisabilityInformationResponse
    {

        [XmlElement(ElementName = "Code", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public string Code;

        [XmlElement(ElementName = "Content", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public Content Content;

        [XmlElement(ElementName = "ResponseDescriptionAr", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public string ResponseDescriptionAr;

        [XmlElement(ElementName = "ResponseDescriptionEn", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public string ResponseDescriptionEn;

        [XmlElement(ElementName = "ResponseTitle", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public string ResponseTitle;

        [XmlElement(ElementName = "validationCode", Namespace = "http://schemas.datacontract.org/2004/07/MOCDAPI.Models")]
        public int ValidationCode;

        [XmlAttribute(AttributeName = "i", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string I;

        [XmlAttribute(AttributeName = "xmlns", Namespace = "")]
        public string Xmlns;

        [XmlText]
        public string Text;
    }





}
