﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Complaint.Responses
{
    public class Complaint
    {
        public Guid Id { get; set; }
        public KeyValue Topic { get; set; }
        public KeyValue Service { get; set; }
        public KeyValue SubService { get; set; }
        public string Title { get; set; }
        public string TicketNumber { get; set; }
        // public string Origin { get; set; }
        public string Description { get; set; }
        public string CaseType { get; set; }
        public int Status { get; set; }
        public int StateCode { get; set; }
        public string PhoneNumber { get; set; }
        public string icpMobileNo { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime ModifiedOn { get; set; }
        public string EmailAddress { get; set; }
        public int AppealStatus { get; set; }
        public string Case { get; set; }
        public bool IsReopen { get; set; }
        public string CaseResolution { get; set; }
        public string CaseResolutionCommentsPortal { get; set; }
    }

    public class KeyValue
    {
        public Guid Id { get; set; }
        public string Text { get; set; }
    }
}
