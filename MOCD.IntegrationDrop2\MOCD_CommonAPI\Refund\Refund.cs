﻿using MOCD_CommonAPI.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Refund
{
    public class RefundTokenResponse
    {
        public string access_token { get; set; }
        public string token_type { get; set; }
        public int expires_in { get; set; }
        public string scope { get; set; }
    }

    public class RefundRequest
    {
        public Guid CaseId { get; set; }
        public Guid BeneficiaryId { get; set; }
        public Guid PaymentOption { get; set; }
    }
    public class ProceedPayment
    {
        public string ResponseUrl { get; set; }
        public string ErrorUrl { get; set; }
        public string LangId { get; set; }
        public List<Transactions> TransactionsList { get; set; }
    }
    public class Transactions
    {
        public Guid Id { get; set; }
        public double Amount { get; set; }
    }
    public class ProceedPaymentRefundRequest
    {
        public string id { get; set; }
        public double amt { get; set; }
        public List<Servicedatum> servicedata { get; set; }
        public string responseURL { get; set; }
        public string errorURL { get; set; }
        public string udf1 { get; set; }
        public string udf2 { get; set; }
        public string udf3 { get; set; }
        public string udf4 { get; set; }
        public string udf5 { get; set; }
        public string udf6 { get; set; }
        public string udf7 { get; set; }
        public string udf8 { get; set; }
        public string udf9 { get; set; }
        public string udf10 { get; set; }
        public string password { get; set; }
        public int action { get; set; }
        public string correlationid { get; set; }
        public string langid { get; set; }
        public string currencyCode { get; set; }
        public string version { get; set; }
    }

    public class InquiryPaymentRefundResponse
    {
        public string auth { get; set; }
        public string inqType { get; set; }
        public string amt { get; set; }
        public List<Servicedatum> servicedata { get; set; }
        public string udf10 { get; set; }
        public string error { get; set; }
        public string @ref { get; set; }
        public string udf9 { get; set; }
        public string respCodeDesc { get; set; }
        public string udf7 { get; set; }
        public string udf8 { get; set; }
        public string customMessage { get; set; }
        public string actionCode { get; set; }
        public string tranid { get; set; }
        public string udf5 { get; set; }
        public string postdate { get; set; }
        public string udf6 { get; set; }
        public string udf3 { get; set; }
        public string udf4 { get; set; }
        public string udf1 { get; set; }
        public string version { get; set; }
        public string udf2 { get; set; }
        public string authRespcode { get; set; }
        public string errorText { get; set; }
        public string paymentid { get; set; }
        public string correlationid { get; set; }
        public string status { get; set; }
    }

    public class InquiryPaymentRefundRequest
    {
        public string action { get; set; }
        public string id { get; set; }
        public string inqType { get; set; }
        public string password { get; set; }
        public string transid { get; set; }
        public string udf5 { get; set; }
        public string version { get; set; }
    }


    public class Servicedatum
    {
        public string amount { get; set; }
        public string noOfTransactions { get; set; }
        public string merchantId { get; set; }
        public string serviceId { get; set; }
    }
    public class ProceedPaymentRefundResponse
    {
        public string status { get; set; }
        public string tokenid { get; set; }
        public string URL { get; set; }
        public string customMessage { get; set; }
        public string errorText { get; set; }
    }
}
