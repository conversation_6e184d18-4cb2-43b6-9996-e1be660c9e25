﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Request
{
    public class CaseDetails
    {
        public Guid IdCase { get; set; }
    }
    public class ResponseCaseById
    {
        public Guid IdCase { get; set; }
        public Guid IdStatus { get; set; }
        public int CaseType { get; set; }
        public Guid ParentCaseId { get; set; }
        public bool eligibleHousing { get; set; }
        public bool eligibleEducation { get; set; }
        public string SubmissionTime { get; set; }
        public string NameEn { get; set; }
        public string NameAr { get; set; }
        public string Email { get; set; }
        public string Mobile { get; set; }
        public decimal TotalRefundAmount {get; set; }
        public bool IsPendingRefund { get; set; }
        public int? RefundFinancialStatus { get; set; }
        public bool IsInflationStandAloneEdit { get; set; }
        public bool IsInflationBaseEdit { get; set; }
        public bool IsInflationNominatedCaseEdit { get; set; }
        public Case CaseDetails { get; set; }
    }

    public class CaseSummaryDetails
    {
        public Guid IdCase { get; set; }
        public string SubmissionTime { get; set; }
        public string NameEn { get; set; }
        public string NameAr { get; set; }
        public string Email { get; set; }
        public string Mobile { get; set; }
        public string CaseRef { get; set; }
        public string CaseType { get; set; }
        public Guid ProcessTemplate { get; set; }
    }
}
