﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.ExternalWrapperServices.Nafis
{
    public class GetAllEligibleEIds
    {
        public string EId { get; set; }
        public string FullName { get; set; }
        public string ArabicFullName { get; set; }
        public string Nationality { get; set; }
        public string Gender { get; set; }
        public DateTime DateOfBirth { get; set; }
        public string MobileNumber { get; set; }
        public string Occupation { get; set; }
        public string Emirates { get; set; }
        public bool IsPrimary { get; set; }
        public bool IsPod { get; set; }
        //    public PoDDetails DisabilityDetails { get; set; }
        public string Level1Disability { get; set; }
        public string SeverityLevel { get; set; }
    }

    public class PoDDetails
    {
        public string Level1Disability { get; set; }
        //   public string Level2Disability { get; set; }
        public string SeverityLevel { get; set; }
    }

    public class GetByEidResponse
    {
        public bool IsEligibleToWork { get; set; }
        public bool IsPrimary { get; set; }
        public bool IsPod { get; set; }
        //  public PoDDetails DisabilityDetails { get; set; }
        public string Level1Disability { get; set; }
        public string SeverityLevel { get; set; }
        public string Message { get; set; }
    }
}
