﻿using MOCD_CommonAPI.Owner;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI
{


    public class GetFinancialBenefitsDetailsRequest
    {
        public string EmiratesId { get; set; }
    }


    public class FinancialBenefitsDetailsResponse
    {
        public FinancialBenefitsDetails Data { get; set; } = new FinancialBenefitsDetails();
        public string Message { get; set; }
        public string ErrorCode { get; set; }
        public bool IsSucceeded { get; set; }
    }


    public class FinancialBenefitsDetails
    {
        public string EmirateID { get; set; }
        public string Name { get; set; }
        public List<object> FinancialBenefits { get; set; }
        public List<object> Payments { get; set; }
        public string HasActiveFinancialBenefits { get; set; }
        public string CreatedOn { get; set; }
    }

}
