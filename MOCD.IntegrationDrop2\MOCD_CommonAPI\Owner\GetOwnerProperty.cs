﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Owner
{
    public class GetOwnerPropertyRequest
    {
        public string OwnerNo { get; set; }
        public string IncludeExpired { get; set; }
    }

    public class OwnerPropertyResponse
    {
        public List<OwnerProperty> Data { get; set; } = new List<OwnerProperty>();
        public int Status { get; set; }
    }


    public class OwnerProperty
    {

        public string id { get; set; }
        public double ActualArea { get; set; }
        public string Address { get; set; }
        public string ApprovalRefNo { get; set; }
        public int AreaId { get; set; }
        public string AreaNameAr { get; set; }
        public string AreaNameEn { get; set; }
        public object BuildingNameAr { get; set; }
        public object BuildingNameEn { get; set; }
        public double ContractAmount { get; set; }
        public int DBAction { get; set; }
        public string Dmno { get; set; }
        public string Dmsubno { get; set; }
        public int Identity { get; set; }
        public int IsFreehold { get; set; }
        public string Landno { get; set; }
        public string Landsubno { get; set; }
        public string PropertyName { get; set; }
        public int PropertyTypeId { get; set; }
        public string ZoneNameAr { get; set; }
        public string ZoneNameEn { get; set; }

    }


}
