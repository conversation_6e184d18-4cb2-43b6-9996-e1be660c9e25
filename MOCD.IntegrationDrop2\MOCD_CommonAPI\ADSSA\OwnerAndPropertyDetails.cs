﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.ADSSA
{
    public class getOwnerAndPropertyDetails_DLDRequest
    {
        public string EmiratesId { get; set; }
    }


    public class Result
    {
        public string HoHEmiratesId { get; set; }
        public string HoHFullName { get; set; }
        public DateTime BeneficiaryDOB { get; set; }
        public DateTime Benefitstartdate { get; set; }
        public string FinancialSupportStatus { get; set; }
        public double SSASupportAmount { get; set; }
        public double Active_MOCD_Amount { get; set; }
        public int Headoffamilyflag { get; set; }
        public string SSAStatus { get; set; }
    }

    public class OwnerAndPropertyDetails_DLDResponce
    {
        public Result Result { get; set; }
    }


}
