﻿using MOCD_CommonAPI.Documents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Complaint.Requests
{
    public class CreateAppealRequest
    {
        public Guid Case { get; set; }
        public Guid Beneficiary { get; set; }
        public string Description { get; set; }
        public List<AttachmentDetails> listAttachments { get; set; }

    }
    public class Appeal
    {
        public Guid Case { get; set; }
        public string Description { get; set; }
    }
}
