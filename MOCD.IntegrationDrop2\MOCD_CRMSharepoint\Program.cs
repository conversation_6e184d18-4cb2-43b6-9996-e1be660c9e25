﻿using Microsoft.SharePoint.Client;
using Microsoft.Xrm.Sdk;
using System;
using System.Configuration;
using System.IO;
using System.Security;
using System.ServiceModel;

namespace CRMSharepoint
{
    class Program
    {
        static void Main(string[] args)
        {

            try
            {
               
                // Sharepoint configuration from App.Config
                string SiteUrl = ConfigurationManager.AppSettings["SPSiteUrl"];
                string userName = ConfigurationManager.AppSettings["SPUserName"];
                string password = ConfigurationManager.AppSettings["SPPassword"];


                // LoadSharepoint password
                var securePassword = new SecureString();
                foreach (char c in password)
                {
                    securePassword.AppendChar(c);
                }
                // Sharepoint object





                // Load Recent Request To Process
                IOrganizationService gblOrgService = CRMConnection.GetCrmConnection();
                var hexa_Requests = MocdHelper.GetLatestApprovedRequestWithDocumentsAttached(gblOrgService);

                //Get RequestDocuments
                if (hexa_Requests != null && hexa_Requests.Count > 0)
                {
                    foreach (hexa_Request _request in hexa_Requests)
                    {
                        var hexa_requestdocuments = MocdHelper.GetRequestDocuments(gblOrgService, _request.hexa_RequestId.Value); 

                        if (hexa_requestdocuments != null && hexa_requestdocuments.Count > 0)
                        {
                            foreach (hexa_RequestDocument hexa_requestdocument in hexa_requestdocuments)
                            {
                                var attachments = MocdHelper.GetAttachmentFromRequestDocuments(gblOrgService, hexa_requestdocument.hexa_RequestDocumentId.Value);

                                if (attachments != null && attachments.Count > 0)
                                {
                                    foreach (Annotation _attachment in attachments)
                                    {
                                       

                                        using (var clientContext = new ClientContext(SiteUrl))
                                        {
                                            string _fileName = _attachment.FileName;
                                            clientContext.Credentials = new SharePointOnlineCredentials(userName, securePassword);
                                            Web web = clientContext.Web;
                                            clientContext.Load(web, a => a.ServerRelativeUrl);
                                            clientContext.ExecuteQuery();
                                            Microsoft.SharePoint.Client.List documentsList = clientContext.Web.Lists.GetByTitle("Beneficiary");

                                            // Download the attachment in the current execution folder.
                                            using (FileStream fileStream = new FileStream(_attachment.FileName, FileMode.OpenOrCreate))
                                            {
                                                byte[] fileContent = Convert.FromBase64String(_attachment.DocumentBody);
                                                // fileStream.Write(fileContent, 0, fileContent.Length);


                                                var fileCreationInformation = new FileCreationInformation();
                                                //Assign to content byte[] i.e. documentStream  

                                                //  fileCreationInformation.Content = System.IO.File.ReadAllBytes(FileName);
                                                fileCreationInformation.Content = fileContent;
                                                //Allow owerwrite of document  

                                                fileCreationInformation.Overwrite = true;
                                                //Upload URL  


                                                ListItemCreationInformation _lciContactFolder = new ListItemCreationInformation();

                                                _lciContactFolder.FolderUrl = SiteUrl + "/contact/";// documentsList.RootFolder.ServerRelativeUrl;
                                                _lciContactFolder.UnderlyingObjectType = Microsoft.SharePoint.Client.FileSystemObjectType.Folder;
                                                _lciContactFolder.LeafName = ((EntityReference)_request.Attributes[hexa_Request.Fields.hexa_PortalContact]).Name + "_" + ((EntityReference)_request.Attributes[hexa_Request.Fields.hexa_PortalContact]).Id.ToString().ToUpper().Replace("-", "");

                                                string foldername = _lciContactFolder.LeafName;


                                                try
                                                {
                                                    Microsoft.SharePoint.Client.ListItem folder = documentsList.AddItem(_lciContactFolder);
                                                    folder.Update();
                                                    clientContext.ExecuteQuery();
                                                }
                                                catch (Exception ex)
                                                {
                                                    Logger.Info("Folder Exists : " + _lciContactFolder.LeafName);
                                                }




                                                //Create a Sub-Folder
                                                ListItemCreationInformation _SubFolder = new ListItemCreationInformation();
                                                _SubFolder.FolderUrl = SiteUrl + "/contact/" + _lciContactFolder.LeafName; //documentsList.RootFolder.ServerRelativeUrl;
                                                _SubFolder.UnderlyingObjectType = Microsoft.SharePoint.Client.FileSystemObjectType.Folder;
                                                _SubFolder.LeafName = hexa_requestdocument.hexa_Name;

                                                string subfoldername = _SubFolder.LeafName; 

                                                try
                                                {

                                                    Microsoft.SharePoint.Client.ListItem SubFolder = documentsList.AddItem(_SubFolder);
                                                    SubFolder.Update();
                                                    clientContext.ExecuteQuery();
                                                }
                                                catch (Exception ex)
                                                {
                                                    Logger.Info("Sub Folder Exists : " + _SubFolder.LeafName);
                                                }



                                                try
                                                {

                                                    fileCreationInformation.Url = SiteUrl + "/contact/" + ((EntityReference)_request.Attributes[hexa_Request.Fields.hexa_PortalContact]).Name + "_" + ((EntityReference)_request.Attributes[hexa_Request.Fields.hexa_PortalContact]).Id.ToString().ToUpper().Replace("-", "") + "/" + hexa_requestdocument.hexa_Name + "/" + _attachment.FileName;
                                                    Microsoft.SharePoint.Client.File uploadFile = documentsList.RootFolder.Files.Add(fileCreationInformation);
                                                    //Update the metadata for a field having name "DocType"  
                                                    uploadFile.ListItemAllFields["Title"] = "UploadedviaBatchJOB";
                                                    uploadFile.ListItemAllFields.Update();
                                                    clientContext.ExecuteQuery();
                                                    Logger.Info("Uploaded To Sharepoint " + fileCreationInformation.Url.ToString()  );
                                                    //Delete attachments after uploading to sharepoint
                                                    gblOrgService.Delete(Annotation.EntityLogicalName, _attachment.AnnotationId.Value);
                                                    Logger.Info("Delete from CRM " + _fileName.ToString());

                                                }
                                                catch (Exception ex)
                                                {
                                                    Logger.Info(ex.ToString());
                                                }

                                            }

                                        }
                                    }

                                }
                            }

                        }
                    }
                }


            }
            
            catch (System.Exception ex)
            {
               Logger.Error(ex.ToString());
            }
            finally
            {
                Logger.Info("Process completed,Press <Enter> to exit.");

            }


           

        }


        public static Folder CreateFolder(Web web, string listTitle, string fullFolderPath)
        {
            if (string.IsNullOrEmpty(fullFolderPath))
                throw new ArgumentNullException("fullFolderPath");
            var list = web.Lists.GetByTitle(listTitle);
            return CreateFolderInternal(web, list.RootFolder, fullFolderPath);
        }
        private static Folder CreateFolderInternal(Web web, Folder parentFolder, string fullFolderPath)
        {
            var folderUrls = fullFolderPath.Split(new char[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
            string folderUrl = folderUrls[0];
            var curFolder = parentFolder.Folders.Add(folderUrl);
            web.Context.Load(curFolder);
            web.Context.ExecuteQuery();
            if (folderUrls.Length > 1)
            {
                var folderPath = string.Join("/", folderUrls, 1, folderUrls.Length - 1);
                return CreateFolderInternal(web, curFolder, folderPath);
            }
            return curFolder;
        }




    }
}


