﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.AidDetailResponse
{
    public class AidDetail
    {
        public string helpCategory { get; set; }
        public int amount { get; set; }
        public string nationality { get; set; }
        public string region { get; set; }
        public string issueDate { get; set; }
    }

    public class Data
    {
        public string identityNo { get; set; }
        public string nationality { get; set; }
        public string aidStatus { get; set; }
        public string familyCard { get; set; }
        public string name { get; set; }
        public List<AidDetail> aidDetails { get; set; }
        public string region { get; set; }
        public int fileId { get; set; }
    }

    public class AidDetailsResponse
    {
        public Data data { get; set; }
        public bool success { get; set; }
    }
}
