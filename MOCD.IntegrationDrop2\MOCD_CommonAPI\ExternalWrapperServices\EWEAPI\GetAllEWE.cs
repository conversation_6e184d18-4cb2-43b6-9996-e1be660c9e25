﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.ExternalWrapperServices.EWEAPI
{
    public class GetAllEWE
    {
        public Guid id { get; set; }
        public string caseRef { get; set; }
        public string eid { get; set; }
        public string accountRef { get; set; }
        public string payoutDate { get; set; }
        //    public string eweConsumption { get; set; }
        //  public decimal? subsidizeAmount { get; set; }
        //public bool isEligible { get; set; }
        //public bool isApprovedByFinance { get; set; }
    }

    public class EweCaseType
    {
        public List<GetAllEWE> newCases { get; set; }
        public List<GetAllEWE> existingCases { get; set; }
        public List<GetAllEWE> rejectedCases { get; set; }
    }

    public class EweCaseResponse
    {
        public EweCaseType casesApprovedByFinanace { get; set; }
        public EweCaseType casesApprovedByAudit { get; set; }

    }

    public class EWErequest
    {
        public int casesFrom { get; set; }
    }
}
