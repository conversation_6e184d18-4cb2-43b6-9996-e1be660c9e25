﻿using Microsoft.Xrm.Sdk;
using MOCD_CommonAPI.Constant;
using MOCD_CommonAPI.OTP.Requests;
using MOCD_CRMWrapperServices.Infrastructure;
using MOCD_CRMWrapperServices.Services;
using MOCD_ExternalAPI.EmiratesOTP;
using MOCD_ExternalAPI.FamilyInformationAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace MOCD_CRMWrapperServices.Controllers
{
    [RoutePrefix("API/OTP")]
    public class OTPController : ApiController
    {
        private static IOrganizationService service;
        public OTPController()
        {
            service = CRMConnection.GetCrmConnection();
        }

        [HttpGet, Route("GenerateOTP")]
        private async Task<IHttpActionResult> GenerateOTP(string emiratesId)
        {
            try
            {
                if (string.IsNullOrEmpty(emiratesId))
                    throw new Exception(CustomMessages.EmiratesId);
                var otp = await CunsumeOTPService.GenerateOTP(service, emiratesId);
                return Ok(Responses.Success(otp));
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        [HttpPost, Route("ValidateOTP")]
        private async Task<IHttpActionResult> ValidateOTP([FromBody] ValidateOTP validateOTP)
        {
            try
            {
                if (string.IsNullOrEmpty(validateOTP.EmiratesId))
                    throw new Exception(CustomMessages.EmiratesId);

                if (validateOTP.Code == 0)
                    throw new Exception(CustomMessages.OTPCode);

                var contact = await CunsumeOTPService.AuthenticationByEmiratesId(validateOTP, service);
                return Ok(Responses.Success(contact));
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }


    }
}