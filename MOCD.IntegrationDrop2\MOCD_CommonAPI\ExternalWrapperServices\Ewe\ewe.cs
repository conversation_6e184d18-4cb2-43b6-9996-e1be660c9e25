﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.ExternalWrapperServices.Ewe
{
    public class AllTransactions
    {
        public string caseRef { get; set; }
        public string eid { get; set; }
        public string accountRef { get; set; }
        public DateTime payoutDate { get; set; }
        public decimal eweConsumption { get; set; }
        public decimal subsidiesAmount { get; set; }
        public bool isEligible { get; set; }
        public bool isApprovedByFinance { get; set; }
    }
}
