﻿using MOCD_CommonAPI.Contact;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.FamilyBook
{
    public class FamilyBook1
    {
        public string FamilyBookId { get; set; }
        public string Name { get; set; }
        public string FamilyHeadId { get; set; }
        public string MotherId { get; set; }
        public string DependantId { get; set; }
        public string RelationshipCode { get; set; }
        public MaritalStatusDetails MaritalStatus { get; set; }
    }

    public class FamilyBookDetails
    {
        public string FamilyBookId { get; set; }
        public string Name { get; set; }
        public string MotherName { get; set; }
        public string RelationshipName { get; set; }
        public FamilyHeadDetails FamilyHead { get; set; }
        public DependantDetails Dependants { get; set; }
    }

    public class FamilyBookMemberDetails
    {
        public string RequestId { get; set; }
        public string EmiratesId { get; set; }
        public string DOB { get; set; }
        public string RelationshipType { get; set; }
    }

    public class FamilyHeadDetails
    {
        public string FamilyHeadId { get; set; }
        public string FullName { get; set; }
    }

    public class DependantDetails
    {
        public string DependantId { get; set; }
        public string FullName { get; set; }
    }

    public class FamilyBookRequest
    {
        public string EmiratesId { get; set; }
        public string PortalSubCategoryCode { get; set; }
        public string Gender { get; set; }
        public string MaritalStatus { get; set; }
        public int? Age { get; set; }
        public bool? IsPOD { get; set; }
    }
}
