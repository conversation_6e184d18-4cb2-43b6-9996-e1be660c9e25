﻿using Microsoft.Xrm.Sdk;
using MOCD.SWP.Integrations.Outbound;
using MOCD_CommonAPI.ExternalWrapperServices.EWEAPI;
using MOCD_CommonAPI.Income;
using MOCD_CommonAPI.Inflation;
using MOCD_CRMWrapperServices.Infrastructure;
using MOCD_CRMWrapperServices.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

namespace MOCD_CRMWrapperServices.Controllers
{
    [RoutePrefix("API/Inflation")]
    public class InflationController : ApiController
    {
        private static IOrganizationService service;
        public InflationController()
        {
            service = CRMConnection.GetCrmConnection();
        }

        [HttpPost, Route("ValidateAccount")]
        public async Task<IHttpActionResult> ValidateAccount(InflationRequest objInflation)
        {
            try
            {
                HttpRequestHeaders headers = this.Request.Headers;
                if (headers.Contains("KEY"))
                {
                    string entityName = headers.GetValues("KEY").First().ToLower();

                    InflationService objInflationService = new InflationService(service);
                    InflationResponse response = await objInflationService.ValidateAccount(objInflation, entityName);

                    return Ok(Responses.Success(response));
                }
                else
                {
                    return Ok(Responses.Fail("Please provide a valid KEY"));
                }
            }
            catch (Exception ex)
            {
                return Ok(Responses.Fail(ex.Message));
            }
        }
    }
}