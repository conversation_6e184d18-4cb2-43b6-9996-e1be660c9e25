﻿using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using Microsoft.Xrm.Sdk.Metadata;

namespace MOCD_CommonAPI.Common
{
    public class GetHistoryJson
    {

        /// <summary>
        ///  Get json string to create history
        /// </summary>
        /// <param name="objEntity">Object of the entity for which history json is required</param>
        /// <returns>history json</returns>
        /// <exception cref="Exception"></exception>
        public static string GetHistoryJsonDetails(IOrganizationService service, Entity objEntity)
        {
            try
            {
                List<HistoryDetails> lstHistoryDetails = new List<HistoryDetails>();
                //Excluding the system attributes from Clone
                var systemAttributes = new List<string>
                {
                    "owninguser",
                    "owningbusinessunit",
                    "ownerid",
                    "createdby",
                    "createdbyname",
                    "createdbyyominame",
                    "createdon",
                    "createdonbehalfby",
                    "createdonbehalfbyname",
                    "createdonbehalfbyyominame",
                    "importsequencenumber",
                    "modifiedby",
                    "modifiedbyname",
                    "modifiedbyyominame",
                    "modifiedon",
                    "modifiedonbehalfby",
                    "modifiedonbehalfbyname",
                    "modifiedonbehalfbyyominame",
                    "organizationid",
                    "organizationidname",
                    "overriddencreatedon",
                    "timezoneruleversionnumber",
                    "utcconversiontimezonecode",
                    "versionnumber",
                    "statecode",
                    "statecodename",
                    "statuscode",
                    "statuscodename",
                    "exchangerate",
                    "transactioncurrencyid"

                };
                foreach (var attribute in objEntity.Attributes.Where(x => !systemAttributes.Contains(x.Key)))
                {

                    HistoryDetails objHistoryDetails = null;
                    var attributeType = attribute.Value.GetType().Name;
                    var attributeSchema = attribute.Key;
                    if (attributeType.ToLower() != "guid" && !attributeSchema.Contains("_timestamp") && !attributeSchema.Contains("_name") && !attributeSchema.Contains("_url") && !attributeSchema.Contains("_base"))
                    {
                        switch (attributeType)
                        {
                            case "EntityReference":

                                var entityRef = attribute.Value as EntityReference;
                                objHistoryDetails = new HistoryDetails
                                {
                                    value = entityRef.Name,
                                };
                                break;
                            case "OptionSetValue":
                            case "OptionSetValueCollection":
                            case "Money":
                            case "Boolean":
                            case "Decimal":
                            case "Double":
                                objHistoryDetails = new HistoryDetails
                                {
                                    value = objEntity.FormattedValues[attribute.Key].ToString()
                                };
                                break;
                            default:

                                objHistoryDetails = new HistoryDetails
                                {
                                    value = attribute.Value
                                };
                                break;
                        }

                        objHistoryDetails.displayName = RetrieveAttributeDisplayName(service, objEntity.LogicalName, attribute.Key);
                        objHistoryDetails.schemaName = attributeSchema;
                        lstHistoryDetails.Add(objHistoryDetails);
                    }
                }
                History objHistory = new History
                {
                    lstHistory = lstHistoryDetails
                };

                string jsonString = JsonSerializer.Serialize<History>(objHistory);

                return jsonString;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// Get Display Name of attribute
        /// </summary>
        /// <param name="EntitySchemaName">schema name of entity for which display name is required</param>
        /// <param name="AttributeSchemaName">schema name of attribute for which display name is required</param>
        /// <returns>string display name of attribute</returns>
        private static string RetrieveAttributeDisplayName(IOrganizationService service, string EntitySchemaName, string AttributeSchemaName)
        {
            try
            {

                RetrieveAttributeRequest retrieveAttributeRequest = new RetrieveAttributeRequest
                {
                    EntityLogicalName = EntitySchemaName,
                    LogicalName = AttributeSchemaName
                };
                RetrieveAttributeResponse retrieveAttributeResponse = (RetrieveAttributeResponse)service.Execute(retrieveAttributeRequest);
                AttributeMetadata retrievedAttributeMetadata = (AttributeMetadata)retrieveAttributeResponse.AttributeMetadata;

                return retrievedAttributeMetadata.DisplayName.UserLocalizedLabel.Label;
            }
            catch (Exception ex)
            {

                return String.Empty;

            }
        }
        /// <summary>
        /// History details
        /// </summary>
        public class HistoryDetails
        {

            public string displayName { get; set; }
            public string schemaName { get; set; }
            public object value { get; set; }
        }

        /// <summary>
        /// List of history items
        /// </summary>
        public class History
        {
            public List<HistoryDetails> lstHistory { get; set; }
        }
    }
}
