﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.TradeLicense
{
    public class TradeLicenseDetailsRequest
    {
        public string emid { get; set; }

    }


    public class Data
    {
        public int id { get; set; }
        public DateTime issueDate { get; set; }
        public string requestIdentityNo { get; set; }
        public string invNameAr { get; set; }
        public string invNameEn { get; set; }
        public string invEmiratesId { get; set; }
        public object resultStatus { get; set; }
        public List<License> licenses { get; set; }
        public object notes { get; set; }
    }

    public class License
    {
        public int id { get; set; }
        public string licId { get; set; }
        public string trdNameAr { get; set; }
        public string trdNameEn { get; set; }
        public string expiryDate { get; set; }
        public string invType { get; set; }
        public string invTypeAr { get; set; }
        public string invTypeEn { get; set; }
        public string licStatus { get; set; }
        public string licStatusAr { get; set; }
        public string licStatusEn { get; set; }
    }

    public class TradeLicenseDetailsResponce
    {
        public bool success { get; set; }
        public Data data { get; set; }
    }



}
