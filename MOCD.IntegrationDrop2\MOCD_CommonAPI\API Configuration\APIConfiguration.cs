﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using MOCD_CommonAPI.Common;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.API_Configuration
{
    public class APIConfiguration
    {

        public static APIConfig GetAPIConfiguration(IOrganizationService service, string _key)
        {
            string _fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                  <entity name='mocd_apiconfiguration'>
                                    <attribute name='mocd_apiconfigurationid' />
                                    <attribute name='mocd_name' />
                                    <attribute name='mocd_additionalparametersjson' />
                                    <attribute name='mocd_endpoint' />
                                    <attribute name='createdon' />
                                    <attribute name='mocd_isscheduledjob' />
                                    <order attribute='mocd_name' descending='false' />
                                    <filter type='and'>
                                      <condition attribute='mocd_name' operator='eq' value='" + _key + @"' />
                                    </filter>
                                  </entity>
                                </fetch>";

            EntityCollection records = service.RetrieveMultiple(new FetchExpression(_fetchxml));

            if (records.Entities != null && records.Entities.Count > 0)
            {
                APIConfig _details = new APIConfig();

                foreach (var item in records.Entities)
                {
                    string _APIName = item.Contains("mocd_name") ? item.GetAttributeValue<string>("mocd_name") : string.Empty;

                    if (_APIName.ToLower() == _key.ToLower())
                    {
                        string json = item.Contains("mocd_additionalparametersjson") ? item.GetAttributeValue<string>("mocd_additionalparametersjson") : string.Empty;
                        dynamic _obj = JsonConvert.DeserializeObject(json);

                        List<Parameters> _params = new List<Parameters>();

                        if (_obj.parameters != null)
                        {
                            foreach (var _item in _obj.parameters)
                            {
                                Parameters _param = new Parameters()
                                {
                                    Name = _item.name,
                                    Value = _item.value,
                                };
                                _params.Add(_param);
                            }
                        }
                        List<Header> headers = new List<Header>();
                        if(_obj.headers != null)
                        {
                            foreach(var _item in _obj.headers)
                            {
                                Header header = new Header
                                {
                                    name = _item.name,
                                    value = _item.value,
                                };
                                headers.Add(header);
                            }
                        }
                        _details = new APIConfig()
                        {
                            APIName = _key,
                            Endpoint = item.Contains("mocd_endpoint") ? item.GetAttributeValue<string>("mocd_endpoint") : string.Empty,
                            Username = _obj.authentication != null ? _obj.authentication.username : string.Empty,
                            Password = _obj.authentication != null ? _obj.authentication.password : string.Empty,
                            TokenURL = _obj.authentication != null ? _obj.authentication.TokenURL : string.Empty,
                            Parameters = _params ,
                            Headers = headers,
                            IsEnable = item.Contains("mocd_isscheduledjob") ? item.GetAttributeValue<bool>("mocd_isscheduledjob") : true,
                        };

                        return _details;
                    }
                }
            }
            return null;
        }
    }
}
