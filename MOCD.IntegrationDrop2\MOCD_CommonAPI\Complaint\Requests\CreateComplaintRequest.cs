﻿using MOCD_CommonAPI.Documents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Complaint.Requests
{
    public class CreateComplaintRequest
    {
        //public Guid TopicId { get; set; }
        //public Guid BeneficiaryId { get; set; }
        public bool IsInternal { get; set; } = false;
        public string EmirateId { get; set; }
        public string Title { get; set; }
        public int Origin { get; set; }
        public string Description { get; set; }
        public int CaseType { get; set; }
        public string Case { get; set; }
        public string PhoneNumber { get; set; }
        public string icpMobileNo { get; set; }
        public string EmailAddress { get; set; }
        public Guid topicId { get; set; }
        public Guid serviceId { get; set; }
        public Guid subServiceId { get; set; }
        public List<AttachmentDetails> listAttachments { get; set; }
    }

    public class ComplaintCreateResponse
    {
        public Guid Id { get; set; }
        public string Title { get; set; }
        public string CaseId { get; set; }

    }
}
