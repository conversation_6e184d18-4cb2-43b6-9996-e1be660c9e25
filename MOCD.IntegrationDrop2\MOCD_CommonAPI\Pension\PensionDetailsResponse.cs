﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Pension
{
    public class PensionDetailsResponse
    {
        public bool succeeded { get; set; }
        public List<PensionDetails> data { get; set; }
        public List<object> errors { get; set; }
    }
    public class PensionDetails
    {
        public string name { get; set; }
        public string customerSegment { get; set; }
        public string customerType { get; set; }
        public string nationality { get; set; }
        public string nonResident { get; set; }
        public string emiratesId { get; set; }
        public string gender { get; set; }
        public DateTime dataOfBirth { get; set; }
        public string familyBookId { get; set; }
        public string familyCityId { get; set; }
        public string familyId { get; set; }
        public string pensionStatus { get; set; }
        public double monthlyPension { get; set; }
        public DateTime pensionStartDate { get; set; }
        public DateTime lastDisbursementDate { get; set; }
    }

}
