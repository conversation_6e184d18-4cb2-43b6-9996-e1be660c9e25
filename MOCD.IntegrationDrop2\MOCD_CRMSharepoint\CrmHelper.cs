﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;


namespace CRMSharepoint
{
    internal class CrmHelper
    {

        /// <summary>
        /// Get configuration details based on key passed
        /// If only one configuration is needed.
        /// </summary>
        /// <param name="key">list of keys for which value is needed</param>
        /// <param name="service">IOrganizationService</param>
        /// <param name="optr">Condition Operator, default is Equal</param>
        /// <returns>Key value pair</returns>
        public static KeyValuePair<string, string> FetchDataFromConfiguration(string key, IOrganizationService service, ConditionOperator optr = ConditionOperator.Equal)
        {
            try
            {
                if (key == string.Empty)
                {
                    throw new Exception("Key cannot be empty");
                }
                QueryExpression queryConfig = new QueryExpression
                {
                    EntityName = hexa_hexaConfiguration.EntityLogicalName,
                    ColumnSet = new ColumnSet(hexa_hexaConfiguration.Fields.hexa_Value, hexa_hexaConfiguration.Fields.hexa_name)
                };

                queryConfig.Criteria.AddCondition(hexa_hexaConfiguration.Fields.hexa_name, optr, key);


                //Retrieving config collection and if any value is encrypted, returning it after decrypting it.
                return service.RetrieveMultiple(queryConfig).Entities.Select(x => x.ToEntity<hexa_hexaConfiguration>()).Select(y => new KeyValuePair<string, string>(y.hexa_name, y.hexa_Value)).FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error in  FetchDataFromConfiguration {ex.Message}", ex);
            }

        }


        public static string FetchKeyValueFromConfiguration(string key, IOrganizationService service, ConditionOperator optr = ConditionOperator.Equal)
        {
            try
            {
                if (key == string.Empty)
                {
                    throw new Exception("Key cannot be empty");
                }
                QueryExpression queryConfig = new QueryExpression
                {
                    EntityName = hexa_hexaConfiguration.EntityLogicalName,
                    ColumnSet = new ColumnSet(hexa_hexaConfiguration.Fields.hexa_Value, hexa_hexaConfiguration.Fields.hexa_name)
                };

                queryConfig.Criteria.AddCondition(hexa_hexaConfiguration.Fields.hexa_name, optr, key);


                //Retrieving config collection and if any value is encrypted, returning it after decrypting it.
                return service.RetrieveMultiple(queryConfig).Entities[0].Attributes[hexa_hexaConfiguration.Fields.hexa_Value].ToString();


            }
            catch (Exception ex)
            {
                throw new Exception($"Error in  FetchValueFromConfiguration {ex.Message}", ex);
            }

        }


        public static string FetchDataFromApiConfiguration(string key, IOrganizationService service, ConditionOperator optr = ConditionOperator.Equal)
        {
            try
            {
                if (key == string.Empty)
                {
                    throw new Exception("Key cannot be empty");
                }
                QueryExpression queryConfig = new QueryExpression
                {
                    EntityName = mocd_apiconfiguration.EntityLogicalName,
                    ColumnSet = new ColumnSet(mocd_apiconfiguration.Fields.mocd_Endpoint, mocd_apiconfiguration.Fields.mocd_name)
                };

                queryConfig.Criteria.AddCondition(mocd_apiconfiguration.Fields.mocd_name, optr, key);


                //Retrieving config collection and if any value is encrypted, returning it after decrypting it.
                return service.RetrieveMultiple(queryConfig).Entities[0].Attributes[mocd_apiconfiguration.Fields.mocd_Endpoint].ToString();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error in  FetchDataFromConfiguration {ex.Message}", ex);
            }

        }


        public static void TraceException(Exception ex, ITracingService tracer)
        {
            System.Diagnostics.StackTrace trace = new System.Diagnostics.StackTrace(ex, true);
            tracer.Trace($"Error in Current Method :- {MethodBase.GetCurrentMethod()} , Error Function : {trace.GetFrame(0).GetMethod().ReflectedType.FullName} , Line: {trace.GetFrame(0).GetFileLineNumber()} , Column : {trace.GetFrame(0).GetFileColumnNumber()} ,error is: {ex.Message}\nError:{ex}");
        }

        public static EntityCollection RetrieveAllRecordsUsingFetchXML(string fetchXML, IOrganizationService orgService, ITracingService tracingService)
        {
            try
            {
                var moreRecords = false;
                int page = 1;
                var cookie = string.Empty;
                var entityCollection = new EntityCollection();
                do
                {
                    var xml = string.Format(fetchXML, cookie);
                    var collection = orgService.RetrieveMultiple(new FetchExpression(xml));

                    if (collection.Entities.Count > 0)
                    {
                        entityCollection.Entities.AddRange(collection.Entities);
                    }

                    moreRecords = collection.MoreRecords;
                    if (moreRecords)
                    {
                        page++;
                        cookie = string.Format("paging-cookie='{0}' page='{1}'", System.Security.SecurityElement.Escape(collection.PagingCookie), page);
                    }
                }
                while (moreRecords);

                return entityCollection;
            }
            catch (Exception ex)
            {
                throw (TraceAndThrowException(ex, tracingService));
            }
        }

        public static Exception TraceAndThrowException(Exception ex, ITracingService tracer)
        {
            System.Diagnostics.StackTrace trace = new System.Diagnostics.StackTrace(ex, true);
            tracer.Trace($"Error in Current Method :- {MethodBase.GetCurrentMethod()} , Error Function : {trace.GetFrame(0).GetMethod().ReflectedType.FullName} , Line: {trace.GetFrame(0).GetFileLineNumber()} , Column : {trace.GetFrame(0).GetFileColumnNumber()} ,error is: {ex.Message}\nError:{ex}");
            throw ex;
        }


    }


       

  
}
