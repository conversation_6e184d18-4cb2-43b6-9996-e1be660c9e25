﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Owner
{
    public class GetOwnerContractRequest
    {
        public string OwnerNo { get; set; }
        public string IncludeExpired { get; set; }
    }

    public class OwnerContractResponse
    {
        public List<OwnerContract> Data { get; set; } = new List<OwnerContract>();
        public int Status { get; set; }
    }


    public class OwnerContract
    {
        public string Land_Number { get; set; }
        public string Land_DM_Number { get; set; }
        public int Land_Area_Id { get; set; }
        public string Land_Area_Name_En { get; set; }
        public string Land_Area_Name_Ar { get; set; }
        public string Building_Name_Ar { get; set; }
        public string Building_Name_En { get; set; }
        public string Contract_Number { get; set; }
        public DateTime Contract_Start_Date { get; set; }
        public DateTime Contract_End_Date { get; set; }
        public double Contract_Amount { get; set; }
        public string Dewa_Premise { get; set; }
        public int Ejari_Sub_Type_Id { get; set; }
        public string Ejari_Sub_Type_Name_Ar { get; set; }
        public string Ejari_Sub_Type_Name_En { get; set; }
        public int Ejari_Type_Id { get; set; }
        public string Ejari_Type_Name_Ar { get; set; }
        public string Ejari_Type_Name_En { get; set; }
        public int Ejari_Property_Type_Id { get; set; }
        public string Property_Number { get; set; }

    }

}
