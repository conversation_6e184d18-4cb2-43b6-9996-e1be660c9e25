﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using MOCD_BatchJobs.Assets;
using MOCD_BatchJobs.Helper;
using MOCD_CommonAPI.API_Configuration;
using MOCD_CommonAPI.Common;
using MOCD_CommonAPI.PODDisabilityInformation;
using MOCD_ExternalAPI.PODDisabilityInformationAPI;
using System;
using System.Threading.Tasks;

namespace MOCD_BatchJobs.BatchJobBusiness
{
    public static class PODMonthlyBatchJobService
    {
        public static async Task FillDisabilityInformations(IOrganizationService service)
        {
            try
            {
                APIConfig DisabilityDetailsAPIConfig = APIConfiguration.GetAPIConfiguration(service, "GetDisabilityInformationByIDAPI");
                var eliableApprovedStatus = Helper.Helper.FetchDataFromConfiguration("EligibleApproved_InternalStatus", service);

                var cases = service.RetrieveAll(new FetchExpression(Helper.FetchXMLs.GetApprovedCasesXML(eliableApprovedStatus.Value)));


                int counter = 0, reset = 0;

                #region familyhead
                if (cases != null && cases.Count > 0)
                {
                    Console.WriteLine("Primary Beneficiaries: " + cases.Count);
                    foreach (Entity record in cases)
                    {
                        counter++;
                        reset++;
                        if (counter < 7100)
                        {
                            string emiratesID = record.GetAttributeValue<string>("mocd_emiratesid");

                            Task<PoDInfo> _details = GetDisabilityInformations(service, DisabilityDetailsAPIConfig, emiratesID);
                            if (_details.Result != null)
                            {
                                Console.WriteLine("POD: " + emiratesID + " - " + counter);
                                hexa_Request _Request = new hexa_Request()
                                {
                                    Id = record.Id
                                };

                                _Request.mocd_level1disability = _details.Result.Level1Disability;
                                _Request.mocd_level2disability = _details.Result.Level2Disability;
                                _Request.mocd_severitylevel = _details.Result.Serverity;

                                _Request.mocd_Level1DisabilityAr = _details.Result.Level1DisabilityAr;
                                _Request.mocd_Level2DisabilityAr = _details.Result.Level2DisabilityAr;
                                _Request.mocd_SeverityLevelAr = _details.Result.ServerityAr;
                                _Request.mocd_IsPOD = true;

                                service.Update(_Request);

                                Contact contact = new Contact()
                                {
                                    Id = record.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_PortalContact).Id
                                };

                                contact.mocd_level1disability = _details.Result.Level1Disability;
                                contact.mocd_level2disability = _details.Result.Level2Disability;
                                contact.mocd_severitylevel = _details.Result.Serverity;

                                contact.mocd_Level1DisabilityAr = _details.Result.Level1DisabilityAr;
                                contact.mocd_Level2DisabilityAr = _details.Result.Level2DisabilityAr;
                                contact.mocd_SeverityLevelAr = _details.Result.ServerityAr;

                                contact.mocd_IsPOD = true;
                                service.Update(contact);
                            }
                            else
                            {
                                Console.WriteLine("Without POD: " + emiratesID + " - " + counter);

                                hexa_Request _Request = new hexa_Request()
                                {
                                    Id = record.Id
                                };

                                _Request.mocd_IsPOD = false;

                                service.Update(_Request);


                                Contact contact = new Contact()
                                {
                                    Id = record.GetAttributeValue<EntityReference>(hexa_Request.Fields.hexa_PortalContact).Id
                                };

                                contact.mocd_IsPOD = false;

                                service.Update(contact);
                            }
                        }
                    }

                }

                counter = 0; reset = 0;
                #endregion

                #region dependants
                var _dependants = service.RetrieveAll(new FetchExpression(Helper.FetchXMLs.GetFamilyBookApprovedCasesXML()));

                if (_dependants != null && _dependants.Count > 0)
                {
                    Console.WriteLine("Dependants: " + _dependants.Count);

                    foreach (Entity dependant in _dependants)
                    {
                        string _emirateid = dependant.Contains("dep.mocd_emiratesid") ? dependant.GetAttributeValue<AliasedValue>("dep.mocd_emiratesid").Value.ToString() : string.Empty;

                        if (!string.IsNullOrWhiteSpace(_emirateid))
                        {
                            Task<PoDInfo> _details = GetDisabilityInformations(service, DisabilityDetailsAPIConfig, _emirateid);
                            reset++;
                            if (_details.Result != null)
                            {
                                Console.WriteLine("POD: " + _emirateid + " - " + counter++);

                                if (dependant.Contains("mocd_dependent"))
                                {
                                    Contact contact = new Contact()
                                    {
                                        Id = dependant.GetAttributeValue<EntityReference>("mocd_dependent").Id
                                    };

                                    contact.mocd_level1disability = _details.Result.Level1Disability;
                                    contact.mocd_level2disability = _details.Result.Level2Disability;
                                    contact.mocd_severitylevel = _details.Result.Serverity;

                                    contact.mocd_Level1DisabilityAr = _details.Result.Level1DisabilityAr;
                                    contact.mocd_Level2DisabilityAr = _details.Result.Level2DisabilityAr;
                                    contact.mocd_SeverityLevelAr = _details.Result.ServerityAr;

                                    contact.mocd_IsPOD = true;

                                    service.Update(contact);
                                }
                            }
                            else
                            {
                                Console.WriteLine("Without POD: " + _emirateid + " - " + counter++);

                                if (dependant.Contains("mocd_dependent"))
                                {
                                    Contact contact = new Contact()
                                    {
                                        Id = dependant.GetAttributeValue<EntityReference>("mocd_dependent").Id
                                    };

                                    contact.mocd_IsPOD = false;

                                    service.Update(contact);
                                }
                            }
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public static async Task<PoDInfo> GetDisabilityInformations(IOrganizationService service, APIConfig DisabilityDetailsAPIConfig, string _emirateid)
        {
            try
            {
                DisabilityInformationResponse DisabilityInformation = await PODDisabilityInformationAPI.GetDisabilityInformationByID(_emirateid, DisabilityDetailsAPIConfig);

                if (DisabilityInformation.Code == "Success")
                {
                    string level2Disability = "", level2DisabilityAr = "";

                    //if (DisabilityInformation.Content.TypeTitleEn.ToLower() == "multiple")
                    //{
                    foreach (MultipleDisabilityDetail multipleDisabilityDetail in DisabilityInformation.Content.MultipleDisabilityDetails.multipleDisabilityDetail)
                    {
                        level2DisabilityAr += multipleDisabilityDetail.TypeTitleAr + " | ";
                        level2Disability += multipleDisabilityDetail.TypeTitleEn + " | ";
                    }
                    //}

                    PoDInfo _obj = new PoDInfo()
                    {
                        Level1DisabilityAr = DisabilityInformation.Content.TypeTitleAr,
                        Level1Disability = DisabilityInformation.Content.TypeTitleEn,
                        Level2Disability = level2Disability,
                        Level2DisabilityAr = level2DisabilityAr,
                        ServerityAr = DisabilityInformation.Content.LevelTitleAr,
                        Serverity = DisabilityInformation.Content.LevelTitleEn
                    };
                    return _obj;
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

    }

    public class PoDInfo
    {
        public string Level1Disability { get; set; }
        public string Level2Disability { get; set; }
        public string Serverity { get; set; }
        public string Level1DisabilityAr { get; set; }
        public string Level2DisabilityAr { get; set; }
        public string ServerityAr { get; set; }
    }
}
