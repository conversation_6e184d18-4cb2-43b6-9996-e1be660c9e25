﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

namespace CRMSharepoint
{
    internal class MocdHelper
    {


        public static List<hexa_RequestDocument> GetRequestDocuments(IOrganizationService orgService, Guid requestID)
        {
            try
            {
                // tracingService.Trace("Start of GetRequestDocuments");

                QueryExpression queryRequestDocuments = new QueryExpression(hexa_RequestDocument.EntityLogicalName);
                queryRequestDocuments.ColumnSet = new ColumnSet(hexa_RequestDocument.Fields.hexa_RequestDocumentId, hexa_RequestDocument.Fields.hexa_Name);
                queryRequestDocuments.Criteria.AddCondition(hexa_RequestDocument.Fields.hexa_Request, ConditionOperator.Equal, requestID);
                var resultRequestDocuments = orgService.RetrieveMultiple(queryRequestDocuments);
                if (resultRequestDocuments != null && resultRequestDocuments.Entities.Count > 0)
                {
                    //   tracingService.Trace(resultRequestDocuments.Entities.Count + " records found in the Request Documents");
                    //  tracingService.Trace("End of GetRequestDocuments");
                    return resultRequestDocuments.Entities.ToList().Select(fb => fb.ToEntity<hexa_RequestDocument>()).ToList();
                }
                else
                {
                    //  tracingService.Trace("No Request Documents Found");
                    return null;
                }
            }
            catch (Exception ex)
            {
                //  tracingService.Trace("Exception While Execution of GetBeneficiaryFamilyBook" + ex.Message);
                return null;
            }
        }



        public static List<hexa_Request> GetLatestApprovedRequestWithDocumentsAttached(IOrganizationService orgService)
        {
            string hexa_internalstatus = ConfigurationManager.AppSettings["hexa_internalstatus"];
            string SPBatchCount = ConfigurationManager.AppSettings["SPBatchCount"];

            try
            {
                // tracingService.Trace("Start of GetRequestDocuments");

                string FetchXML = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='true' top='" + SPBatchCount + @"' >
                                    <entity name='hexa_request' > 
                                        <attribute name='hexa_requestid' />
                                        <attribute name='hexa_portalcontact' /> 
                                        <order attribute='modifiedon' descending='true' />
                                        <filter type='and' >
                                            <condition attribute='hexa_internalstatus' operator='eq' uiname='EligibleÂ /Â Approved' uitype='hexa_processstatustemplate' value='{" + hexa_internalstatus + @"}' />
                                        </filter>
                                        <link-entity name='hexa_requestdocument' from='hexa_request' to='hexa_requestid' link-type='inner' alias='ac' >
                                            <link-entity name='annotation' from='objectid' to='hexa_requestdocumentid' link-type='inner' alias='ad' />
                                        </link-entity>
                                    </entity>
                                </fetch>";

                var resulthexa_Request = orgService.RetrieveMultiple(new FetchExpression(FetchXML));
                if (resulthexa_Request != null && resulthexa_Request.Entities.Count > 0)
                {
                    return resulthexa_Request.Entities.ToList().Select(fb => fb.ToEntity<hexa_Request>()).ToList();
                }
                else
                {

                    return null;
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }


        public static List<Annotation> GetAttachmentFromRequestDocuments(IOrganizationService orgService, Guid requestDocumentId)
        {
            QueryExpression queryGetRequestDocuments = new QueryExpression(Annotation.EntityLogicalName);
            queryGetRequestDocuments.ColumnSet = new ColumnSet(true);
            queryGetRequestDocuments.Criteria.AddCondition(Annotation.Fields.ObjectId, ConditionOperator.Equal, requestDocumentId);
            queryGetRequestDocuments.Criteria.AddCondition(Annotation.Fields.ObjectTypeCode, ConditionOperator.Equal, hexa_RequestDocument.EntityLogicalName);
            var resultsRequestDocumentAttachments = orgService.RetrieveMultiple(queryGetRequestDocuments);
            if (resultsRequestDocumentAttachments != null && resultsRequestDocumentAttachments.Entities.Count > 0)
            {
                return resultsRequestDocumentAttachments.Entities.ToList().Select(fb => fb.ToEntity<Annotation>()).ToList();
            }
            else
            {

                return null;
            }
        }
    }









}
