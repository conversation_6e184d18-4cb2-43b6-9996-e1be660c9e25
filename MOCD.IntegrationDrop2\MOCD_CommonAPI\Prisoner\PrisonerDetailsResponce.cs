﻿using MOCD_CommonAPI.Owner;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Prisoner
{
    public class PrisonerDetailsResponce
    {
        public content content { get; set; } = new content();

    }
    public class PrisonerDetailsRequest
    {
        public int UnifiedNumber { get; set; }

    }

    public class content
    {
        public string EmiratesId { get; set; }
        public string ArabicFullName { get; set; }
        public string EnglishFullName { get; set; }
        public DateTime EntryDate { get; set; }
        public DateTime ReleaseDate { get; set; }
        public DateTime ExpectedDate { get; set; }
        public object Description { get; set; }
        public object CRF_File_No { get; set; }
        public object FileCode { get; set; }
    }



}
