﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Common
{
    public class Status
    {
        public string Value { get; set; }
        public int Key { get; set; }
    }
    public class Gender
    {
        public string Value { get; set; }
        public int Key { get; set; }
    }

    public class Header
    {
        public string name { get; set; }
        public string value { get; set; }
    }


    public class APIConfig
    {
        public string APIName { get; set; }
        public string Endpoint { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string TokenURL { get; set; }

        public bool IsEnable { get; set; } = true;
        public List<Header> Headers { get; set; }
        public List<Parameters> Parameters { get; set; }
    }

    public class Parameters
    {
        public string Name { get; set; }
        public string Value { get; set; }
    }

    public class Lookup
    {

        public string EntityName { get; set; }
        public Guid Id { get; set; }
        public string name { get; set; }
    }

    /// <summary>
    /// English and arabic message on portal
    /// </summary>
    public class ErrorMessage
    {
        public string MessageEn { get; set; }
        public string MessageAr { get; set; }

    }
}
