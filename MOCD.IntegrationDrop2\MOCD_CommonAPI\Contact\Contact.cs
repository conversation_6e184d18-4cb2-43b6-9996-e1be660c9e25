﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MOCD_CommonAPI.Nationality;


namespace MOCD_CommonAPI.Contact
{
    public class ContactLookup
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }

    public class RetrieveContactResponse
    {
        public DateTime DateofBirth { get; set; }
        public int Age { get; set; } //
        public int EmiratesCode { get; set; } //
        public int EmploymentTypeCode { get; set; } //
        public Nationality.Nationality Nationality { get; set; }
        public Common.Status UniversityEnrollmentStatus { get; set; } //
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string LastName { get; set; }
        public string FirstNameArabic { get; set; }
        public string MiddleNameArabic { get; set; }
        public string LastNameArabic { get; set; }
        public string FullNameArabic { get; set; }
        public string EmiratesID { get; set; }
        public string ICAEmail { get; set; }
        public string ICAPhoneNumber { get; set; }
        public string PreferredEmail { get; set; } //
        public string PreferredPhoneNumber { get; set; } //
        public string gender { get; set; }
        public Common.Gender GenderCode { get; set; }
        public string PassportNumber { get; set; }
        public Guid Occupation { get; set; }
        public string CountryOfBirth { get; set; }
        public MaritalStatusDetails MaritalStatus { get; set; }
        public string ContactId { get; set; }
        public string Image { get; set; }
        public ContactLookup Mother { get; set; }
        public string City { get; set; }
        public string Area { get; set; }
        public string IDNBackNumber { get; set; }
        public string KhulasitQaidNo { get; set; }
        public bool IsExistingBeneficiary { get; set; }
        public int NationalityCode { get; set; }
    }

    public class MaritalStatusDetails
    {
        public Guid MaritalStatusId { get; set; }
        public string Name { get; set; }
        public string NameAr { get; set; }
        public string Code { get; set; }
    }
}
