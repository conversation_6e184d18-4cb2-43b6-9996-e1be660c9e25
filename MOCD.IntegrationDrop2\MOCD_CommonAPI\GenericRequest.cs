﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI
{
    public class GenericRequest
    {
        public string idCase { get; set; }
        public List<BeneficiaryDetail> beneficiaryDetails { get; set; }
    }

    public class BeneficiaryDetail
    {
        public string idBeneficiary { get; set; }
        public string eid { get; set; }
        public string familyBookNumber { get; set; }
        public string idCaseFamilyBook { get; set; }
    }

}
