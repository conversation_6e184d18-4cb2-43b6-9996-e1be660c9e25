﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D95DD7E8-6E6D-4F49-921B-646EDF143C32}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MOCD_CommonAPI</RootNamespace>
    <AssemblyName>MOCD_CommonAPI</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.6.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Crm.Sdk.Proxy, Version=9.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CrmSdk.CoreAssemblies.9.0.2.48\lib\net462\Microsoft.Crm.Sdk.Proxy.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Xrm.Sdk, Version=9.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CrmSdk.CoreAssemblies.9.0.2.48\lib\net462\Microsoft.Xrm.Sdk.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.DirectoryServices.AccountManagement" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Text.Encodings.Web, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.6.0.0\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=6.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.6.0.2\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AccountDetails\Request\AccountDetailsRequest.cs" />
    <Compile Include="AccountDetails\Response\AccountDetail.cs" />
    <Compile Include="ADRPBF\SalaryAPI\SalaryDetailsRequest.cs" />
    <Compile Include="ADRPBF\SalaryAPI\SalaryDetailsResponse.cs" />
    <Compile Include="ADSSA\OwnerAndPropertyDetails.cs" />
    <Compile Include="AidDetailResponse\GetAidDetailsResponse.cs" />
    <Compile Include="API Configuration\APIConfiguration.cs" />
    <Compile Include="BlockchainApostilles\BlockchainErrorResponse.cs" />
    <Compile Include="BlockchainApostilles\BlockchainApostillesRequest.cs" />
    <Compile Include="BlockchainApostilles\BlockchainApostillesResponse.cs" />
    <Compile Include="Case\Case.cs" />
    <Compile Include="Case\ToWhomItMayConcernsRequest.cs" />
    <Compile Include="Class1.cs" />
    <Compile Include="Common\Common.cs" />
    <Compile Include="Common\CustomMessages.cs" />
    <Compile Include="Common\GetHistoryJson.cs" />
    <Compile Include="Company\GetCompanyDetailsRequest.cs" />
    <Compile Include="Complaint\Requests\CreateAppealRequest.cs" />
    <Compile Include="Complaint\Requests\CreateComplaintRequest.cs" />
    <Compile Include="Complaint\Requests\FeedbackRequest.cs" />
    <Compile Include="Complaint\Responses\Complaint.cs" />
    <Compile Include="Constant\CustomMessages.cs" />
    <Compile Include="Contact\Contact.cs" />
    <Compile Include="Contact\Request\CreateContactRequest.cs" />
    <Compile Include="Contact\Request\UpdateContectRequest.cs" />
    <Compile Include="Documents\Document.cs" />
    <Compile Include="Documents\DocumentRequest.cs" />
    <Compile Include="ExternalWrapperServices\EWEAPI\GetAllEWE.cs" />
    <Compile Include="ExternalWrapperServices\EWEAPI\PushEWEToCRM.cs" />
    <Compile Include="ExternalWrapperServices\Ewe\ewe.cs" />
    <Compile Include="ExternalWrapperServices\Nafis\GetAllEligibleEIds.cs" />
    <Compile Include="ExternalWrapperServices\Nafis\GetAllEligibleIndividualsEIDsResponse.cs" />
    <Compile Include="FamilyBook\DeleteFamilyBookRequest.cs" />
    <Compile Include="FamilyBook\FamilyBook.cs" />
    <Compile Include="Employee\GetEmployeeDetailsRequest.cs" />
    <Compile Include="FinancialBenefits\GetFinancialBenefitsDetails.cs" />
    <Compile Include="GenericRequest.cs" />
    <Compile Include="Income\Income.cs" />
    <Compile Include="Inflation\Inflation.cs" />
    <Compile Include="Job\JobDetailsResponse.cs" />
    <Compile Include="Master\Responses\MasterResponse.cs" />
    <Compile Include="Mohesr\MohesrResponse.cs" />
    <Compile Include="Mohesr\MohesrStudent.cs" />
    <Compile Include="Nationality\Nationality.cs" />
    <Compile Include="OTP\Requests\ValidateOTP.cs" />
    <Compile Include="OTP\Responses\GenerateOTP.cs" />
    <Compile Include="Owner\GetOwnerContract.cs" />
    <Compile Include="Owner\GetOwnerInquiry.cs" />
    <Compile Include="Owner\GetOwnerProperty.cs" />
    <Compile Include="Pension\PensionDetailsResponse.cs" />
    <Compile Include="PODDisabilityInformation\DisabilityInformationResponse.cs" />
    <Compile Include="PortalNotification\PortalNotification.cs" />
    <Compile Include="Prisoner\PrisonerDetailsResponce.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Refund\Refund.cs" />
    <Compile Include="Request\Case.cs" />
    <Compile Include="Request\Request.cs" />
    <Compile Include="Request\ResponseCaseById.cs" />
    <Compile Include="SubCategory\Response\SubCategoryResponse.cs" />
    <Compile Include="Tenancy\TenancyDetailsResponse.cs" />
    <Compile Include="TinyUrl\TinyUrlResponse.cs" />
    <Compile Include="TradeLicense\TradeLicenseDetailsResponce.cs" />
    <Compile Include="TradeLicense\TradeLicenseResponce.cs" />
    <Compile Include="UAEPass\UAEPassRequest.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\System.Text.Json.6.0.2\build\System.Text.Json.targets" Condition="Exists('..\packages\System.Text.Json.6.0.2\build\System.Text.Json.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\System.Text.Json.6.0.2\build\System.Text.Json.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\System.Text.Json.6.0.2\build\System.Text.Json.targets'))" />
  </Target>
</Project>