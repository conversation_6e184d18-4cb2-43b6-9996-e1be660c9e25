﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using MOCD.SWP.Helpers;
using MOCD_CommonAPI.ExternalWrapperServices.EWEAPI;
using MOCD_CommonAPI.Request;
using MOCD_CRMWrapperServices.Assets;
using MOCD_CRMWrapperServices.Infrastructure;
using MOCD_CRMWrapperServices.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Lifetime;
using System.Threading.Tasks;
using System.Web.Http;

namespace MOCD_CRMWrapperServices.Controllers
{
    [RoutePrefix("API/Request")]
    public class RequestController : ApiController
    {
        private static IOrganizationService service;
        public RequestController()
        {
            service = CRMConnection.GetCrmConnection();
        }


        [HttpPost, Route("CreateRequest")]
        public IHttpActionResult CreateRequest(Request request)
        {
            var _request = RequestService.CreateRequest(service, request);
            return Ok(Responses.Success(_request));
        }

        [HttpPatch, Route("UpdateRequest")]
        public IHttpActionResult UpdateRequest(Request request)
        {
            var _request = RequestService.UpdateRequest(service, request);
            return Ok(Responses.Success(_request));
        }

        [HttpGet, Route("RetrieveRequest")]
        public IHttpActionResult RetrieveRequest(string _requestId)
        {
            var _request = RequestService.RetrieveRequest(service, _requestId);
            return Ok(Responses.Success(_request));
        }

        [HttpPost, Route("RetrieveAllRequests")]
        public IHttpActionResult RetrieveAllRequests(string _emirateId)
        {
            var _request = RequestService.RetrieveAllRequests(service, _emirateId);
            return Ok(Responses.Success(_request));
        }

        [HttpGet, Route("RetrieveRequests")]
        public IHttpActionResult RetrieveRequests()
        {
            var _request = RequestService.RetrieveRequests(service);
            return Ok(Responses.Success(_request));
        }

        [HttpGet, Route("RetrieveAllowanceTransactions")]
        public IHttpActionResult RetrieveAllowanceTransactions(string _emirateId)
        {
            var _request = RequestService.RetrieveAllowanceTransactions(service, _emirateId);
            return Ok(Responses.Success(_request));
        }

        [HttpGet, Route("GetCaseRequestByCaseId")]
        public IHttpActionResult GetCaseRequestByCaseId(Guid caseId, Guid beneficiaryId)
        {
            var _request = RequestService.GetCaseRequestByCaseId(caseId, beneficiaryId, service);
            return Ok(Responses.Success(_request));
        }

        [HttpGet, Route("GetCasePersonalDetails")]
        public IHttpActionResult GetCasePersonalDetails(Guid caseId, Guid beneficiaryId)
        {
            var _request = RequestService.GetCasePersonalDetails(caseId, beneficiaryId, service);
            return Ok(Responses.Success(_request));
        }

        [HttpGet, Route("GetCaseFamilyHeadListDetails")]
        public IHttpActionResult GetCaseFamilyHeadListDetails(Guid caseId, Guid beneficiaryId)
        {
            var _request = RequestService.GetCaseFamilyHeadListDetails(caseId, beneficiaryId, service);
            return Ok(Responses.Success(_request));
        }

        [HttpGet, Route("GetCaseFamilyMembersListDetails")]
        public async Task<IHttpActionResult> GetCaseFamilyMembersListDetails(Guid caseId, Guid beneficiaryId, bool isCategoryChange)
        {
            try
            {
                bool isUpdated = true;
                CaseFamilyMembersDetails _request = null;
                List<mocd_familybook> familyBookMembers = service.RetrieveMultiple(new FetchExpression(FetchXMLHelper.RetrieveBeneficiaryRelatedFamilyBook(beneficiaryId))).Entities.Select(record => record.ToEntity<mocd_familybook>()).ToList();

                Entity entity = service.Retrieve(hexa_Request.EntityLogicalName, caseId, new ColumnSet(true));
                var SubCategory = entity.Contains(hexa_Request.Fields.mocd_subcategory) ? entity.GetAttributeValue<EntityReference>(hexa_Request.Fields.mocd_subcategory).Id : Guid.Empty;

                bool isPODSubCat = (SubCategory == new Guid("093eb27d-9257-ee11-be6f-6045bd14ccdc") ? true : false);

                var legacyCaseType = entity.Contains("mocd_legacycasetype") ? entity.GetAttributeValue<OptionSetValue>("mocd_legacycasetype").Value : 0;

                var parentCaseId = entity.Contains("hexa_parentrequest") ? entity.GetAttributeValue<EntityReference>("hexa_parentrequest").Id : Guid.Empty;
                bool caseForPODChild = true;
                if (parentCaseId != Guid.Empty)
                {
                    caseForPODChild = entity.Contains("mocd_isthecaseforpodchild") ? entity.GetAttributeValue<bool>("mocd_isthecaseforpodchild") : false;
                }
                bool isPODChild = isPODSubCat && (legacyCaseType == 0 || legacyCaseType == 662410000) && caseForPODChild;


                if (isCategoryChange || familyBookMembers.Count == 0 || isPODChild)
                {
                    isUpdated = await FamilyBookService.CreateFbForCase(service, caseId, isPODChild);
                    _request = RequestService.GetCaseFamilyMembersListDetails(caseId, beneficiaryId, service);

                    List<ChildrenDetails> ListofChildren = null;
                    if (_request != null && _request.ListofChildren != null && _request.ListofChildren.Count > 0)
                    {
                        ListofChildren = _request.ListofChildren;
                    }
                    RequestService.DeleteEducationalAllowance(caseId, ListofChildren, service);
                }

                if (_request == null)
                {
                    _request = RequestService.GetCaseFamilyMembersListDetails(caseId, beneficiaryId, service);
                }

                if (_request != null && _request.ListofChildren != null && _request.ListofChildren.Count > 0 && !isPODChild)
                {
                    RequestService.CreateEducationalAllowance(caseId, _request.ListofChildren, service);
                    _request.ListofChildren = RequestService.GetChildrenByCaseId(caseId, service);
                }

                _request.IsUpdate = isUpdated;

                return Ok(Responses.Success(_request));
            }
            catch (Exception ex)
            {
                return Ok(Responses.Fail(ex.Message + ex.StackTrace));
            }
        }

        [HttpGet, Route("GetCaseFamilyMembersListDetailsFarmerService")]
        public IHttpActionResult GetCaseFamilyMembersListDetailsFarmerService(Guid caseId, Guid beneficiaryId)
        {
            var _request = RequestService.GetCaseFamilyMembersListDetailsFarmerService(caseId, beneficiaryId, service);
            return Ok(Responses.Success(_request));
        }

        [HttpGet, Route("GetCaseSummaryDetails")]
        public IHttpActionResult GetCaseSummaryDetails(Guid caseId, Guid beneficiaryId)
        {
            var _request = RequestService.GetCaseSummaryDetails(caseId, beneficiaryId, service);
            return Ok(Responses.Success(_request));
        }

        [HttpGet, Route("GetCaseEducationalAllowance")]
        public IHttpActionResult GetCaseEducationalAllowance(Guid caseId)
        {
            try
            {
                var _request = RequestService.GetCaseEducationalAllowance(caseId, service);
                return Ok(Responses.Success(_request));
            }
            catch (Exception ex)
            {
                return Ok(Responses.Fail(ex.Message + ex.StackTrace));
            }
        }

        [HttpGet, Route("CheckAllowanceEligibility")]
        public IHttpActionResult CheckAllowanceEligibility(string EmiratesId)
        {
            try
            {
                var _request = RequestService.CheckAllowanceEligibility(service, EmiratesId);
                return Ok(Responses.Success(_request));
            }
            catch (Exception ex)
            {
                return Ok(Responses.Fail(ex.Message + ex.StackTrace));
            }
        }

        [HttpGet, Route("CheckInflationAllowanceEligibility")]
        public IHttpActionResult CheckInflationAllowanceEligibility(Guid ContactId)
        {
            try
            {
                var _request = RequestService.CheckInflationAllowanceEligibility(service, ContactId);
                return Ok(Responses.Success(_request));
            }
            catch (Exception ex)
            {
                return Ok(Responses.Fail(ex.Message + ex.StackTrace));
            }
        }

        [HttpGet, Route("GetCaseRefundInstallments")]
        public async Task<IHttpActionResult> GetCaseRefundInstallments(Guid caseId)
        {
            try
            {
                var _request = await RequestService.GetCaseRefundInstallments(caseId, service);
                return Ok(Responses.Success(_request));
            }
            catch (Exception ex)
            {
                return Ok(Responses.Fail(ex.Message + ex.StackTrace));
            }
        }
    }
}