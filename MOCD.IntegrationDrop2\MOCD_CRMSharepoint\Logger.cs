﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Web;

namespace CRMSharepoint
{

    /// <summary>
    /// This class contains the methods to write the logs.
    /// </summary>
    public static class Logger
    {

        /// <summary>
        /// This method writes the log into console and Trace information.
        /// </summary>
        /// <param name="message"></param>
        public static void Console(string message)
        {
            Trace.TraceInformation(message);
        }

        /// <summary>
        /// This method will store the full detailed log events.
        /// </summary>
        /// <param name="message"></param>
        public static void Verbose(string message)
        {
            Trace.WriteLine(message);
        }

        /// <summary>
        /// Trace Event log information
        /// </summary>
        /// <param name="message"></param>
        public static void Info(string message)
        {
            Trace.TraceInformation(message);
        }

        /// <summary>
        /// This method will log the Warnings
        /// </summary>
        /// <param name="message"></param>
        public static void Warn(string message)
        {
            Trace.TraceWarning(message);
        }

        /// <summary>
        /// This method will log the Exceptions
        /// </summary>
        /// <param name="message"></param>
        public static void Error(string message)
        {
            Trace.TraceError($"Exception: {message}");
        }

        /// <summary>
        /// This method will log the Exceptions
        /// </summary>
        /// <param name="message"></param>
        /// <param name="ex"></param>
        public static void Error(string message, Exception ex)
        {
            Trace.TraceError($"Exception: {message}, Details: {ex.Message}");
        }
    }
}