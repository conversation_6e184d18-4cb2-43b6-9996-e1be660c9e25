﻿using Microsoft.Xrm.Sdk;
using MOCD_CRMWrapperServices.Infrastructure;
using MOCD_CRMWrapperServices.Services;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System;
using System.Web.Http;
using MOCD_CommonAPI.Refund;
using MOCD_CommonAPI.Request;

namespace MOCD_CRMWrapperServices.Controllers
{
    [RoutePrefix("API/Refund")]
    public class RefundController : ApiController
    {
        private static IOrganizationService service;
        public RefundController()
        {
            service = CRMConnection.GetCrmConnection();
        }

        [HttpPost, Route("RefundBreakdown")]
        public async Task<IHttpActionResult> RefundBreakdown(RefundRequest objRefundRequest)
        {
            try
            {
                await RefundService.RefundBreakdown(service, objRefundRequest);
                return Ok(Responses.Success(string.Empty));
            }
            catch (Exception ex)
            {
                return Ok(Responses.Fail(ex.Message));
            }
        }

        [HttpPost, Route("RefundBreakdownSimulator")]
        public async Task<IHttpActionResult> RefundBreakdownSimulator(RefundRequest objRefundRequest)
        {
            try
            {
                var _response=await RefundService.RefundBreakdownSimulator(service, objRefundRequest);
                return Ok(Responses.Success(_response));
            }
            catch (Exception ex)
            {
                return Ok(Responses.Fail(ex.Message));
            }
        }

        [HttpPost, Route("PurchaseRequest")]
        public async Task<IHttpActionResult> ProceedPayment(ProceedPayment objProceedPayment)
        {
            try
            {
                var response = await RefundService.ProceedPayment(service, objProceedPayment);
                return Ok(Responses.Success(response));
            }
            catch (Exception ex)
            {
                return Ok(Responses.Fail(ex.Message));
            }
        }

        [HttpGet, Route("InquiryRequest")]
        public async Task<IHttpActionResult> InquiryPayment(string TransactionId)
        {
            try
            {
                var response = await RefundService.InquiryPayment(service, TransactionId);
                return Ok(Responses.Success(response));
            }
            catch (Exception ex)
            {
                return Ok(Responses.Fail(ex.Message));
            }
        }
    }
}