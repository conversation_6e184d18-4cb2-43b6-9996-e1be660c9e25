﻿
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
// Created via this command line: "C:\Users\<USER>\AppData\Roaming\MscrmTools\XrmToolBox\Plugins\DLaB.EarlyBoundGenerator\crmsvcutil.exe" /url:"https://mocdwave2dev.api.crm15.dynamics.com" /namespace:"MOCD_BatchJobs.Assets" /SuppressGeneratedCodeAttribute /out:"C:\Users\<USER>\AppData\Roaming\MscrmTools\XrmToolBox\Settings\EBG\OptionSets.cs" /codecustomization:"DLaB.CrmSvcUtilExtensions.OptionSet.CustomizeCodeDomService,DLaB.CrmSvcUtilExtensions" /codegenerationservice:"DLaB.CrmSvcUtilExtensions.OptionSet.CustomCodeGenerationService,DLaB.CrmSvcUtilExtensions" /codewriterfilter:"DLaB.CrmSvcUtilExtensions.OptionSet.CodeWriterFilterService,DLaB.CrmSvcUtilExtensions" /namingservice:"DLaB.CrmSvcUtilExtensions.NamingService,DLaB.CrmSvcUtilExtensions" /metadataproviderservice:"DLaB.CrmSvcUtilExtensions.BaseMetadataProviderService,DLaB.CrmSvcUtilExtensions" 
//------------------------------------------------------------------------------

namespace MOCD_BatchJobs.Assets
{


    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_AccountCategoryCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Preferred Customer", 0)]
        PreferredCustomer = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Standard", 1)]
        Standard = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_AccountClassificationCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_AccountRatingCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address1_AddressTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Bill To", 0)]
        BillTo = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 3)]
        Other = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Primary", 2)]
        Primary = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ship To", 1)]
        ShipTo = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address1_FreightTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("FOB", 0)]
        FOB = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No Charge", 1)]
        NoCharge = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address1_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Airborne", 0)]
        Airborne = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("DHL", 1)]
        DHL = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("FedEx", 2)]
        FedEx = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Full Load", 5)]
        FullLoad = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Postal Mail", 4)]
        PostalMail = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("UPS", 3)]
        UPS = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Will Call", 6)]
        WillCall = 7,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address2_AddressTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address2_FreightTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address2_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_BusinessTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_CustomerSizeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_CustomerTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Competitor", 0)]
        Competitor = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Consultant", 1)]
        Consultant = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Customer", 2)]
        Customer = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Influencer", 5)]
        Influencer = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Investor", 3)]
        Investor = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 11)]
        Other = 12,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Partner", 4)]
        Partner = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Press", 6)]
        Press = 7,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Prospect", 7)]
        Prospect = 8,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Reseller", 8)]
        Reseller = 9,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Supplier", 9)]
        Supplier = 10,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Vendor", 10)]
        Vendor = 11,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_IndustryCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Accounting", 0)]
        Accounting = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Agriculture and Non-petrol Natural Resource Extraction", 1)]
        AgricultureandNonpetrolNaturalResourceExtraction = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Broadcasting Printing and Publishing", 2)]
        BroadcastingPrintingandPublishing = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Brokers", 3)]
        Brokers = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Building Supply Retail", 4)]
        BuildingSupplyRetail = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Business Services", 5)]
        BusinessServices = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Consulting", 6)]
        Consulting = 7,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Consumer Services", 7)]
        ConsumerServices = 8,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Design, Direction and Creative Management", 8)]
        DesignDirectionandCreativeManagement = 9,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Distributors, Dispatchers and Processors", 9)]
        DistributorsDispatchersandProcessors = 10,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Doctor\'s Offices and Clinics", 10)]
        DoctorsOfficesandClinics = 11,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Durable Manufacturing", 11)]
        DurableManufacturing = 12,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Eating and Drinking Places", 12)]
        EatingandDrinkingPlaces = 13,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Entertainment Retail", 13)]
        EntertainmentRetail = 14,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Equipment Rental and Leasing", 14)]
        EquipmentRentalandLeasing = 15,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Financial", 15)]
        Financial = 16,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Food and Tobacco Processing", 16)]
        FoodandTobaccoProcessing = 17,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inbound Capital Intensive Processing", 17)]
        InboundCapitalIntensiveProcessing = 18,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inbound Repair and Services", 18)]
        InboundRepairandServices = 19,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Insurance", 19)]
        Insurance = 20,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Legal Services", 20)]
        LegalServices = 21,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Non-Durable Merchandise Retail", 21)]
        NonDurableMerchandiseRetail = 22,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Outbound Consumer Service", 22)]
        OutboundConsumerService = 23,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Petrochemical Extraction and Distribution", 23)]
        PetrochemicalExtractionandDistribution = 24,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Service Retail", 24)]
        ServiceRetail = 25,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("SIG Affiliations", 25)]
        SIGAffiliations = 26,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Social Services", 26)]
        SocialServices = 27,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Special Outbound Trade Contractors", 27)]
        SpecialOutboundTradeContractors = 28,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Specialty Realty", 28)]
        SpecialtyRealty = 29,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Transportation", 29)]
        Transportation = 30,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Utility Creation and Distribution", 30)]
        UtilityCreationandDistribution = 31,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Vehicle Retail", 31)]
        VehicleRetail = 32,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Wholesale", 32)]
        Wholesale = 33,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_mocd_TypeofAccount
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom It May Concern", 0, "#0000ff")]
        ToWhomItMayConcern = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_OwnershipCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 3)]
        Other = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Private", 1)]
        Private = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Public", 0)]
        Public = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Subsidiary", 2)]
        Subsidiary = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_PaymentTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("2% 10, Net 30", 1)]
        _210Net30 = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 30", 0)]
        Net30 = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 45", 2)]
        Net45 = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 60", 3)]
        Net60 = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_PreferredAppointmentDayCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Friday", 5)]
        Friday = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Monday", 1)]
        Monday = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Saturday", 6)]
        Saturday = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sunday", 0)]
        Sunday = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Thursday", 4)]
        Thursday = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Tuesday", 2)]
        Tuesday = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Wednesday", 3)]
        Wednesday = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_PreferredAppointmentTimeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Afternoon", 1)]
        Afternoon = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Evening", 2)]
        Evening = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Morning", 0)]
        Morning = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_PreferredContactMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Any", 0)]
        Any = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Email", 1)]
        Email = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Fax", 3)]
        Fax = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mail", 4)]
        Mail = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Phone", 2)]
        Phone = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_TerritoryCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum activitypointer_DeliveryPriorityCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("High", 2)]
        High = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Low", 0)]
        Low = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Normal", 1)]
        Normal = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_AccountRoleCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Decision Maker", 0)]
        DecisionMaker = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Employee", 1)]
        Employee = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Influencer", 2)]
        Influencer = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address1_AddressTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Bill To", 0)]
        BillTo = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 3)]
        Other = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Primary", 2)]
        Primary = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ship To", 1)]
        ShipTo = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address1_FreightTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("FOB", 0)]
        FOB = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No Charge", 1)]
        NoCharge = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address1_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Airborne", 0)]
        Airborne = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("DHL", 1)]
        DHL = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("FedEx", 2)]
        FedEx = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Full Load", 5)]
        FullLoad = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Postal Mail", 4)]
        PostalMail = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("UPS", 3)]
        UPS = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Will Call", 6)]
        WillCall = 7,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address2_AddressTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address2_FreightTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address2_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address3_AddressTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address3_FreightTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address3_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_CustomerSizeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_CustomerTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_EducationCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_FamilyStatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Divorced", 2)]
        Divorced = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Married", 1)]
        Married = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Single", 0)]
        Single = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Widowed", 3)]
        Widowed = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_GenderCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Female", 1)]
        Female = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Male", 0)]
        Male = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_HasChildrenCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_LeadSourceCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_msdyn_decisioninfluencetag
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Blocker", 2, "#FF0000")]
        Blocker = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Decision maker", 0, "#32C100")]
        Decisionmaker = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Influencer", 1, "#FFD74B")]
        Influencer = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Unknown", 3, "#E1DFDD")]
        Unknown = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_msdyn_orgchangestatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ignore", 2, "#0000ff")]
        Ignore = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No Feedback", 0, "#0000ff")]
        NoFeedback = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not at Company", 1, "#0000ff")]
        NotatCompany = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_PaymentTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("2% 10, Net 30", 1)]
        _210Net30 = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 30", 0)]
        Net30 = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 45", 2)]
        Net45 = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 60", 3)]
        Net60 = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_PreferredAppointmentDayCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Friday", 5)]
        Friday = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Monday", 1)]
        Monday = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Saturday", 6)]
        Saturday = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sunday", 0)]
        Sunday = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Thursday", 4)]
        Thursday = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Tuesday", 2)]
        Tuesday = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Wednesday", 3)]
        Wednesday = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_PreferredAppointmentTimeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Afternoon", 1)]
        Afternoon = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Evening", 2)]
        Evening = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Morning", 0)]
        Morning = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_PreferredContactMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Any", 0)]
        Any = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Email", 1)]
        Email = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Fax", 3)]
        Fax = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mail", 4)]
        Mail = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Phone", 2)]
        Phone = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_TerritoryCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Action_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_actionactiontype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Account", 1, "#0000ff")]
        Account = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contact", 2, "#0000ff")]
        Contact = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Current Step", 3, "#0000ff")]
        CurrentStep = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Custom Code", 9, "#0000ff")]
        CustomCode = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Initiate Sub-Request", 5, "#0000ff")]
        InitiateSubRequest = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Linked Request", 6, "#0000ff")]
        LinkedRequest = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Loop Task", 4, "#0000ff")]
        LoopTask = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Request", 0, "#0000ff")]
        Request = 110000008,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Send Email", 7, "#0000ff")]
        SendEmail = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Send SMS", 8, "#0000ff")]
        SendSMS = 110000004,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_AssignTo
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Team", 1, "#0000ff")]
        Team = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("User", 0, "#0000ff")]
        User = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_BusinessRule_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Condition_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ConditionOperator
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contains", 6, "#0000ff")]
        Contains = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contains Data", 10, "#0000ff")]
        ContainsData = 110000010,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contain Values", 8, "#0000ff")]
        ContainValues = 110000008,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Does Not Contain", 7, "#0000ff")]
        DoesNotContain = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Does Not Contain Data", 11, "#0000ff")]
        DoesNotContainData = 110000011,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Does Not Contain Values", 9, "#0000ff")]
        DoesNotContainValues = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Equals", 0, "#0000ff")]
        Equals = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Greater Than", 2, "#0000ff")]
        GreaterThan = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Greater Than Equal To", 4, "#0000ff")]
        GreaterThanEqualTo = 110000004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Less Than", 3, "#0000ff")]
        LessThan = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Less Than Equal To", 5, "#0000ff")]
        LessThanEqualTo = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not Equals", 1, "#0000ff")]
        NotEquals = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_customcodeconfiguration_hexa_Type
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Action", 0, "#0000ff")]
        Action = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Condition", 1, "#0000ff")]
        Condition = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Email", 2, "#0000ff")]
        Email = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sms", 3, "#0000ff")]
        Sms = 110000002,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_customcodeconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_customcomponentconfiguration_hexa_ConfigurationFor
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Demo", 1, "#0000ff")]
        Demo = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Document Viewer", 0, "#0000ff")]
        DocumentViewer = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_customcomponentconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_DocumentTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_environmentconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_hexaConfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_pricelist_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_pricelistitem_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_processbuilder_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ProcessDocumentTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ProcessPriceItemTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ProcessStatusTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_processstatustemplatetype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0, "#0000ff")]
        Active = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved", 1, "#0000ff")]
        Approved = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Cancelled", 2, "#0000ff")]
        Cancelled = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Rejected", 3, "#0000ff")]
        Rejected = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ProcessStepTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_processsteptemplatesteptype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Document Check", 0, "#0000ff")]
        DocumentCheck = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Quick Action", 1, "#0000ff")]
        QuickAction = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ProcessTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_product_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_hexa_RequestType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Linked Request", 0, "#0000ff")]
        LinkedRequest = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sub Request", 1, "#0000ff")]
        SubRequest = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_DocumentTemplate
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Exception", 0, "#0000ff")]
        Exception = 662410006,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - Female with Family", 4, "#0000ff")]
        ICPorOtherFemalewithFamily = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - Individual Female", 2, "#0000ff")]
        ICPorOtherIndividualFemale = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - Individual Male", 1, "#0000ff")]
        ICPorOtherIndividualMale = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - Male with Family", 3, "#0000ff")]
        ICPorOtherMalewithFamily = 662410002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - No Allowance Female", 6, "#0000ff")]
        ICPorOtherNoAllowanceFemale = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - No Allowance Male", 5, "#0000ff")]
        ICPorOtherNoAllowanceMale = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom It May Concern - Individual Female", 9, "#0000ff")]
        ToWhomItMayConcernIndividualFemale = 662410011,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom It May Concern - Individual Male", 10, "#0000ff")]
        ToWhomItMayConcernIndividualMale = 662410009,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom It May Concern - No Allowance Female", 7, "#0000ff")]
        ToWhomItMayConcernNoAllowanceFemale = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom It May Concern - No Allowance Male", 8, "#0000ff")]
        ToWhomItMayConcernNoAllowanceMale = 662410008,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_entityaddressed
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 1, "#0000ff")]
        Other = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom it May Concern", 0, "#0000ff")]
        ToWhomitMayConcern = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_entityreceivedfrom
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Community Development Authority – Dubai", 2, "#0000ff")]
        CommunityDevelopmentAuthorityDubai = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Department of Community development - Abu Dhabi", 1, "#0000ff")]
        DepartmentofCommunitydevelopmentAbuDhabi = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ministry of Community Development", 0, "#0000ff")]
        MinistryofCommunityDevelopment = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Social Services Department - Sharjah", 3, "#0000ff")]
        SocialServicesDepartmentSharjah = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_lettertype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Family Member", 1, "#0000ff")]
        FamilyMember = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Own", 0, "#0000ff")]
        Own = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0, "#0000ff")]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved", 3, "#0000ff")]
        Approved = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Cancelled", 2, "#0000ff")]
        Cancelled = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Rejected", 1, "#0000ff")]
        Rejected = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_RequestDocument_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0, "#0000ff")]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved", 5, "#0000ff")]
        Approved = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Generated", 6, "#0000ff")]
        Generated = 110000004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 4, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Upload", 2, "#0000ff")]
        PendingUpload = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Re-Upload", 1, "#0000ff")]
        ReUpload = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Uploaded", 3, "#0000ff")]
        Uploaded = 110000002,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_RequestPriceItem_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Added", 0, "#0000ff")]
        Added = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Booked", 1, "#0000ff")]
        Booked = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Consumed", 3, "#0000ff")]
        Consumed = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 4, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Invoiced", 2, "#0000ff")]
        Invoiced = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_RequestStep_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_StepStatusTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_stepstatustemplatetype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("End", 2, "#0000ff")]
        End = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Intermediate", 1, "#0000ff")]
        Intermediate = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Start", 0, "#0000ff")]
        Start = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_StepTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_StepTransitionTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_TransitionTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_CaseOriginCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Customer Pulse", 19, "#0000ff")]
        CustomerPulse = 662410019,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Diwan Shaikh", 14, "#0000ff")]
        DiwanShaikh = 662410014,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Face to Face", 1, "#0000ff")]
        FacetoFace = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("FNC - Minister\'s Office", 15, "#0000ff")]
        FNCMinistersOffice = 662410015,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Her excellency the Minister", 17, "#0000ff")]
        HerexcellencytheMinister = 662410017,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Her excellency the Under Secretary", 18, "#0000ff")]
        HerexcellencytheUnderSecretary = 662410018,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Internal Audit", 13, "#0000ff")]
        InternalAudit = 662410013,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Live Chat on Website", 6, "#0000ff")]
        LiveChatonWebsite = 662410006,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mobile version of the Website - Chatbot", 8, "#0000ff")]
        MobileversionoftheWebsiteChatbot = 662410008,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("MOCD App", 7, "#0000ff")]
        MOCDApp = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mystery Shopper", 12, "#0000ff")]
        MysteryShopper = 662410012,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("NABD", 16, "#0000ff")]
        NABD = 662410016,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Radio Station", 11, "#0000ff")]
        RadioStation = 662410011,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Social Media", 10, "#0000ff")]
        SocialMedia = 662410010,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ticketing Reach", 0, "#0000ff")]
        TicketingReach = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Website - Communicate with the Leadership", 4, "#0000ff")]
        WebsiteCommunicatewiththeLeadership = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Website - Inquiry", 5, "#0000ff")]
        WebsiteInquiry = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Website - Quality Email", 3, "#0000ff")]
        WebsiteQualityEmail = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Website - Suggestions and Complaints", 2, "#0000ff")]
        WebsiteSuggestionsandComplaints = 662410002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("WhatsApp", 9, "#0000ff")]
        WhatsApp = 662410009,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_CaseTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Complaint", 0, "#FF8C00")]
        Complaint = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inquiry", 2, "#E71022")]
        Inquiry = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Request Service", 1, "#0072C6")]
        RequestService = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Suggestion", 3, "#0000ff")]
        Suggestion = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_ContractServiceLevelCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Bronze", 2)]
        Bronze = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Gold", 0)]
        Gold = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Silver", 1)]
        Silver = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_CustomerSatisfactionCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Dissatisfied", 3)]
        Dissatisfied = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Neutral", 2)]
        Neutral = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Satisfied", 1)]
        Satisfied = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Very Dissatisfied", 4)]
        VeryDissatisfied = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Very Satisfied", 0)]
        VerySatisfied = 5,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_FirstResponseSLAStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 0)]
        InProgress = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Nearing Noncompliance", 1)]
        NearingNoncompliance = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Noncompliant", 3)]
        Noncompliant = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Succeeded", 2)]
        Succeeded = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_IncidentStageCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_PriorityCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("High", 0)]
        High = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Low", 2)]
        Low = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Normal", 1)]
        Normal = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_ResolveBySLAStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 0)]
        InProgress = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Nearing Noncompliance", 1)]
        NearingNoncompliance = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Noncompliant", 3)]
        Noncompliant = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Succeeded", 2)]
        Succeeded = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_SeverityCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Cancelled", 2, "#EDEBE9")]
        Cancelled = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Information Provided", 1, "#CFE4FA")]
        InformationProvided = 1000,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 4, "#E7EFFF")]
        InProgress = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Merged", 3, "#806c00")]
        Merged = 2000,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("On Hold", 5, "#EEDBEB")]
        OnHold = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Problem Solved", 0, "#CAEAD8")]
        ProblemSolved = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Researching", 7, "#FBEEB5")]
        Researching = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Waiting for Details", 6, "#F9DCD1")]
        WaitingforDetails = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_accomodationtype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_address_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancebudget_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancecategory_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancegroup_mocd_ConsiderIncome
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("False", 1, "#0000ff")]
        False = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("True", 0, "#0000ff")]
        True = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancegroup_mocd_Entity
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case(Hexa_Request)", 0, "#0000ff")]
        Case_Hexa_Request = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contact", 1, "#0000ff")]
        Contact = 662410001,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancegroup_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancepayout_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Disbursed", 3, "#0000ff")]
        Disbursed = 662410002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft", 0, "#0000ff")]
        Draft = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 6, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("On Hold", 5, "#0000ff")]
        OnHold = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Approvals", 4, "#0000ff")]
        PendingApprovals = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Processed", 2, "#0000ff")]
        Processed = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ready for Processing", 1, "#0000ff")]
        ReadyforProcessing = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancepayoutdetail_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft", 0, "#0000ff")]
        Draft = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 5, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("On Hold", 4, "#0000ff")]
        OnHold = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Approvals", 3, "#0000ff")]
        PendingApprovals = 662410002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Processed", 2, "#0000ff")]
        Processed = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ready for Processing", 1, "#0000ff")]
        ReadyforProcessing = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancesubcategory_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancetransaction_mocd_NewAllowanceStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Amount Decreased but Maintained", 1, "#0000ff")]
        AmountDecreasedbutMaintained = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Amount Increased", 0, "#0000ff")]
        AmountIncreased = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ineligible & Amount Maintained", 2, "#0000ff")]
        IneligibleAmountMaintained = 662410002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Same Amount", 3, "#0000ff")]
        SameAmount = 662410003,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancetransaction_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved", 3, "#0000ff")]
        Approved = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Disbursed", 4, "#0000ff")]
        Disbursed = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft", 0, "#0000ff")]
        Draft = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 5, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending for processing", 1, "#0000ff")]
        Pendingforprocessing = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ready for review", 2, "#0000ff")]
        Readyforreview = 662410002,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancetransactiondetails_mocd_IsPOD
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No", 0, "#0000ff")]
        No = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Yes", 1, "#0000ff")]
        Yes = 662410001,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancetransactiondetails_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_apiconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_area_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_bank_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_beneficiaryallowancegroup_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_beneficiaryhistory_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_BeneficiaryIncome_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_beneficiarypension_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_BeneficiaryRentalIncome_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_beneficiarytradelicense_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_BeneficiaryType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Beneficiary", 0, "#0000ff")]
        Beneficiary = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Family", 1, "#0000ff")]
        Family = 662410001,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casebeneficiaryincome_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casebeneficiarypension_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casebeneficiaryrentalincome_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casebeneficiarytradelicense_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casefamilybook_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casetype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_category_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_childallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_city_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_country_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_DataSource
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation Data", 2, "#0000ff")]
        InflationData = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Legacy Data", 1, "#0000ff")]
        LegacyData = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Legacy Data-New Rule", 0, "#0000ff")]
        LegacyDataNewRule = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Local Entity Data", 3, "#0000ff")]
        LocalEntityData = 662410002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 4, "#0000ff")]
        Other = 662410003,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_education_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_emirates_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_EmploymentStatus_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_EmploymentType_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_familybook_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_familyrelationship_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_gender_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_housecondition_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_houseownership_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_incomesource_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_incometype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_individualallowance_mocd_IsDisabled
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("1", 2)]
        _1 = 100000000,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("False", 1, "#0000ff")]
        False = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("NULL", 3)]
        NULL = 100000001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("True", 0, "#0000ff")]
        True = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_individualallowance_mocd_IsPOD
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("1", 2)]
        _1 = 100000000,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("False", 1, "#0000ff")]
        False = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("NULL", 3)]
        NULL = 100000001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("True", 0, "#0000ff")]
        True = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_individualallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_intermediateexternalapiresponse_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_jobtitle_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_letterservicefamilybook_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_maritalstatus_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_nationality_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_occupation_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_passport_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_passporttype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_pensionauthority_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_pensiontype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_personaccommodated_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_portalnotifications_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Read", 1, "#0000ff")]
        Read = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Unread", 0, "#0000ff")]
        Unread = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_rejectreason_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_religion_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_rentalsource_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_requestallowancegroup_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_siblingallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_sms_InstanceTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not Recurring", 0)]
        NotRecurring = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Exception", 3)]
        RecurringException = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Future Exception", 4)]
        RecurringFutureException = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Instance", 2)]
        RecurringInstance = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Master", 1)]
        RecurringMaster = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_sms_PriorityCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("High", 2)]
        High = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Low", 0)]
        Low = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Normal", 1)]
        Normal = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_sms_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Canceled", 2)]
        Canceled = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Completed", 1)]
        Completed = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Open", 0)]
        Open = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Scheduled", 3)]
        Scheduled = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_spouseallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_street_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_thresholdconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_tribe_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_YesNoBlank
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Information Is Not Available", 2, "#FF0000")]
        InformationIsNotAvailable = 662410002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No", 1, "#0000ff")]
        No = 662410001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Yes", 0, "#0000ff")]
        Yes = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum msft_DataState
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default", 0)]
        Default = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Retain", 1)]
        Retain = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum ServiceStage
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Identify", 0)]
        Identify = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Research", 1)]
        Research = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Resolve", 2)]
        Resolve = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum SocialActivity_PostMessageType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Private Message", 1)]
        PrivateMessage = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Public Message", 0)]
        PublicMessage = 0,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum SocialProfile_Community
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Apple Messages For Business", 11, "#0000ff")]
        AppleMessagesForBusiness = 16,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Cortana", 0, "#0000ff")]
        Cortana = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Direct Line", 1, "#0000ff")]
        DirectLine = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Direct Line Speech", 3, "#0000ff")]
        DirectLineSpeech = 8,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Email", 4, "#0000ff")]
        Email = 9,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Facebook", 15, "", "Facebook item.")]
        Facebook = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Google\'s Business Messages", 12, "#0000ff")]
        GooglesBusinessMessages = 17,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("GroupMe", 5, "#0000ff")]
        GroupMe = 10,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Kik", 6, "#0000ff")]
        Kik = 11,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Line", 13, "#0000ff")]
        Line = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Microsoft Teams", 2, "#0000ff")]
        MicrosoftTeams = 7,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 17, "", "Other default")]
        Other = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Skype", 8, "#0000ff")]
        Skype = 13,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Slack", 9, "#0000ff")]
        Slack = 14,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Telegram", 7, "#0000ff")]
        Telegram = 12,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Twitter", 16, "", "Twitter.")]
        Twitter = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Wechat", 14, "#0000ff")]
        Wechat = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("WhatsApp", 10, "#0000ff")]
        WhatsApp = 15,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum appaction_ClientType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Browser", 0, "#0000ff")]
        Browser = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mobile", 1, "#0000ff")]
        Mobile = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mail App", 2, "#0000ff")]
        MailApp = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum card_Sizes
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Small", 0, "#0000ff")]
        Small = 200000000,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Medium", 1, "#0000ff")]
        Medium = 200000001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Large", 2, "#0000ff")]
        Large = 200000002,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum msdyn_forecastsettingsandsummary_msdyn_ForecastJobStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Success", 0, "#0000ff")]
        Success = 192350000,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Data Failure", 1, "#0000ff")]
        DataFailure = 192350001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pipeline Failure", 2, "#0000ff")]
        PipelineFailure = 192350002,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum msdyn_knowledgemanagementsetting_msdyn_actionlist
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Link / unlink article", 0, "#0000ff")]
        Linkunlinkarticle = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Copy URL", 1, "#0000ff")]
        CopyURL = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Link article and email URL", 2, "#0000ff")]
        LinkarticleandemailURL = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Link article and send article content", 3, "#0000ff")]
        Linkarticleandsendarticlecontent = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum msdyn_oc_daysofweek
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sun", 0, "#0000ff", "Sunday")]
        Sun = 192350000,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mon", 1, "#0000ff", "Monday")]
        Mon = 192350001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Tue", 2, "#0000ff", "Tuesday")]
        Tue = 192350002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Wed", 3, "#0000ff", "Wednesday")]
        Wed = 192350003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Thu", 4, "#0000ff", "Thursday")]
        Thu = 192350004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Fri", 5, "#0000ff", "Friday")]
        Fri = 192350005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sat", 6, "#0000ff", "Saturday")]
        Sat = 192350006,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum msdyn_msdyn_requirementrelationship_msdyn_resourcegroupings
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Organizational Unit", 0, "#0000ff")]
        OrganizationalUnit = 192350000,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Related Resource Pools", 1, "#0000ff")]
        RelatedResourcePools = 192350001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Location", 2, "#0000ff")]
        Location = 192350002,
    }
}
