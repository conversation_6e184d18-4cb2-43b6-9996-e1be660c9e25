﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.ExternalWrapperServices.EWEAPI
{
    public class PushEWEToCRMRequest
    {

        public Guid id { get; set; }
        public string caseRef { get; set; }
        public string eid { get; set; }
        public string accountRef { get; set; }
        public DateTime payoutDate { get; set; }
        public string eweConsumption { get; set; }
        public decimal? subsidizeAmount { get; set; }

    }

    public class response
    {
        public string isSuccess { get; set; }
    }
}
