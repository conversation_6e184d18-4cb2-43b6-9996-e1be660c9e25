﻿using Microsoft.Xrm.Sdk;
using MOCD_CommonAPI.Documents;
using MOCD_CommonAPI.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Income
{
    public class Income
    {
        public List<SalaryIncomeDetails> ListSalaryIncome { get; set; }
        public List<PensionIncomeDetails> ListPensionIncome { get; set; }
        public List<TradeIncomeDetails> ListTradeIncome { get; set; }
        public List<RentalIncomeDetails> ListRentalIncome { get; set; }
    }

    public class SalaryIncomeDetails
    {
        public Guid Id { get; set; }
        public string StatusCode { get; set; }
        public string IncomeType { get; set; }
        public Guid IncomeSource { get; set; }
        public decimal Income { get; set; }
        public string Key { get; set; }
        public string FullNameArabic { get; set; }
        public string FullName { get; set; }
    }

    public class PensionIncomeDetails
    {
        public Guid Id { get; set; }
        public string StatusCode { get; set; }
        public string PensiontType { get; set; }
        public Guid PensiontAuthority { get; set; }
        public decimal PensiontAmount { get; set; }
        public string Key { get; set; }
        public string FullNameArabic { get; set; }
        public string FullName { get; set; }
    }

    public class TradeIncomeDetails
    {
        public Guid Id { get; set; }
        public string StatusCode { get; set; }
        public decimal Income { get; set; }
        public Guid TradeSource { get; set; }
        public string Key { get; set; }
        public string FullNameArabic { get; set; }
        public string FullName { get; set; }
    }

    public class RentalIncomeDetails
    {
        public Guid Id { get; set; }
        public string StatusCode { get; set; }
        public DateTime? ContractStartDate { get; set; }
        public string ContractNo { get; set; }
        public DateTime? ContractEndDate { get; set; }
        public Guid RentalSource { get; set; }
        public decimal RentAmount { get; set; }
        public string FullNameArabic { get; set; }
        public string FullName { get; set; }
    }
}
