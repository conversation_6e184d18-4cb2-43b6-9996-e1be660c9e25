﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Contact.Request
{
    public class CreateContactRequest
    {
        public string ContactId { get; set; }
        public string FirstName { get; set; }
        public string SecondName { get; set; }
        public string UUID { get; set; }
        public string ThirdName { get; set; }
        public string FourthName { get; set; }
        public string FamilyName { get; set; }
        public string LastName { get; set; }
        public string ArabicFirstName { get; set; }
        public string ArabicSecondName { get; set; }
        public string ArabicThirdName { get; set; }
        public string ArabicFourthName { get; set; }
        public string ArabicFamilyName { get; set; }
        public string ArabicLastName { get; set; }
        public string ArabicFullName { get; set; }
        public int NationalityCode { get; set; }
        public string EmiratesID { get; set; }
        public string Email { get; set; }
        public DateTime DateOfBirth { get; set; }
        public int Gender { get; set; }
        public int Emirate { get; set; }
        public string PhoneNumber { get; set; }
        public string FamilyBookNumber { get; set; }
        public string PassportNumber { get; set; }
        public string JobTitle { get; set; }
        public MaritalStatusDetails MaritalStatus { get; set; }
        public DateTime EmiratesIDExpiryDate { get; set; }
        public string IDNBackNumber { get; set; }
        public string City { get; set; }
        public string Area { get; set; }
        public Guid OccupationId { get; set; }
        public string KhulasitQaidNo { get; set; }
    }
}
