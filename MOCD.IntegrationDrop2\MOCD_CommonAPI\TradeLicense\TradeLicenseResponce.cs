﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.TradeLicense
{
    public class TradeLicenseResponce
    {
        public string CBLSSubmissionID { get; set; }
        public List<Message> Message { get; set; } = new List<Message>();
        public List<BLList> BLList { get; set; } = new List<BLList> { };
        public string OwnerCBLSId { get; set; }
        public string OwnerFullNameAR { get; set; }
        public string OwnerFullNameEN { get; set; }
        public string UnifiedId { get; set; }
        public string EmiratesId { get; set; }
        public string PassportNo { get; set; }
    }

    public class BLList
    {
        public string BLCBLSId { get; set; }
        public string EDLicenseID { get; set; }
        public string LicenseNameEn { get; set; }
        public string LicenseNameAr { get; set; }
        public DateTime LicenseExiryDate { get; set; }
        public DateTime LicenseLastModifyDate { get; set; }
        public int IssuanceEDID { get; set; }
        public string IssuanceEDAR { get; set; }
        public string IssuanceEDEN { get; set; }
        public int LicenseTypeID { get; set; }
        public string LicenseTypeAR { get; set; }
        public string LicenseTypeEN { get; set; }
        public int OwnerRelationshipTypeID { get; set; }
        public string OwnerRelationshipTypeEn { get; set; }
        public string OwnerRelationshipTypeAr { get; set; }
        public string OwnerSharePercentage { get; set; }
    }

    public class Message
    {
        public int MessageType { get; set; }
        public int MessageCode { get; set; }
        public string MessageTextAR { get; set; }
        public string MessageTextEN { get; set; }
    }

    public class TradeLicenseRequest
    {
        public string PersonNameAr { get; set; }
        public string PersonNameEn { get; set; }
        public string UnifiedId { get; set; }
        public string PassportNo { get; set; }
        public string EmiratesId { get; set; }
    }

}