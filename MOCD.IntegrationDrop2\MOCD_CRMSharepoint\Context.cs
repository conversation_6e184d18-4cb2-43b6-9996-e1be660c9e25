﻿// <copyright file="Context.cs" company="PwC.DigitalServices">
//     PwC Digital Services. All rights reserved.
// </copyright>
// <author><PERSON><PERSON><PERSON><PERSON></author>

using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System;
using System.Collections.Generic;
using System.Activities;
using Microsoft.Xrm.Sdk.Workflow;


namespace CRMSharepoint
{
   

    /// <summary>
    /// Used to form dynamic queries with all the fields for various entities
    /// </summary>
    public class Context
    {

        /// <summary>
        /// Gets or sets the organisation service
        /// </summary>
        internal readonly IOrganizationService gblOrgService;

        /// <summary>
        /// Gets or sets the Tracing Service.
        /// </summary>
        internal readonly ITracingService gblTracingService;

        /// <summary>
        /// Get and set workflow context
        /// </summary>
        internal readonly IWorkflowContext gblWorkflowContext;



        public Context(CodeActivityContext executionContext)
        {
            gblWorkflowContext = executionContext.GetExtension<IWorkflowContext>();
            IOrganizationServiceFactory serviceFactory = executionContext.GetExtension<IOrganizationServiceFactory>();
            gblOrgService = serviceFactory.CreateOrganizationService(gblWorkflowContext.UserId);
            gblTracingService = executionContext.GetExtension<ITracingService>();
            gblTracingService.Trace($"Target entity retrieved. With Context depth : { gblWorkflowContext.Depth} , Message : {gblWorkflowContext.MessageName} ,  Entity : {gblWorkflowContext.PrimaryEntityName} , Stage : {gblWorkflowContext.StageName} , CorrelationId : {gblWorkflowContext.CorrelationId} ");
        }


    }
}
