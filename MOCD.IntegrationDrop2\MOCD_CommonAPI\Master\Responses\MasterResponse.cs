﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Master.Responses
{
    public class MasterResponse
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string NameAR { get; set; }
        public string Code { get; set; }
        public Guid RelatedId { get; set; }
    }

    public class RelatedResponse
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string NameAR { get; set; }
        public Related Related { get; set; }
    }
    public class Related
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }
}
