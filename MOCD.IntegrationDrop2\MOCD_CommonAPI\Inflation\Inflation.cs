﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Inflation
{
    public class InflationRequest
    {
        public string AccountNumber { get; set; }
        public Guid ParentCaseId { get; set; }
        public Guid CaseId { get; set; }
        public string Provider { get; set; }
        public string InflationType { get; set; }
    }

    public class InflationResponse
    {
        public string EmiratesID { get; set; }
        public bool? AccountStatus { get; set; }
        public bool? AccountType { get; set; }
        public bool? UAENational { get; set; }
        public string BillingCycle { get; set; }
        public bool? ReceivingInflationAllowance { get; set; }
        public bool IsValidEID { get; set; }
    }

    public class DEWAInflationAddress
    {
        public string HouseNumber { get; set; }
        public string Street { get; set; }
        public string District { get; set; }
        public string PostalCode { get; set; }
        public string City { get; set; }
    }
    public class AADCInflationData
    {
        public string EID { get; set; }
        public string ACC_Status { get; set; }
        public string ACC_Type { get; set; }
        public string UAE_National { get; set; }
        public string Bill_Cycle { get; set; }
        public string receivingInflationallowances { get; set; }
        public string SocialCardallowance { get; set; }
        public string statuscode { get; set; }
        public string statusMessage { get; set; }
    }
    public class ADDCInflationData
    {
        public string EID { get; set; }
        public string ACC_Status { get; set; }
        public string ACC_Type { get; set; }
        public string UAE_National { get; set; }
        public string Bill_Cycle { get; set; }
        public string receivingInflationallowances { get; set; }
        public string SocialCardallowance { get; set; }
        public string statuscode { get; set; }
        public string statusMessage { get; set; }
    }


    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class Address
    {
        public string houseNumber { get; set; }
        public string street { get; set; }
        public string district { get; set; }
        public string postalCode { get; set; }
        public string city { get; set; }
    }

    public class Body
    {
        public KeyData keyData { get; set; }
        public PremiseDetailsResp premiseDetailsResp { get; set; }
        public Message message { get; set; }
    }

    public class GetPremiseDetailsResponse
    {
        public Header Header { get; set; }
        public Body Body { get; set; }
    }

    public class Header
    {
        public string serviceName { get; set; }
        public string serviceVersion { get; set; }
        public string serviceLang { get; set; }
        public string hashvalue { get; set; }
    }

    public class KeyData
    {
        public string transactionRefNo { get; set; }
        public DateTime timeStamp { get; set; }
    }

    public class Message
    {
        public string type { get; set; }
        public string code { get; set; }
        public string description { get; set; }
    }

    public class PremiseDetailsResp
    {
        public List<DEWAInflationData> record { get; set; }
    }

  

    public class DEWAInflationRootData
    {
        public GetPremiseDetailsResponse GetPremiseDetailsResponse { get; set; }
    }



    public class DEWAInflationData
    {
        public string InputIdType { get; set; }
        public string InputIdNumber { get; set; }
        public string PremiseNo { get; set; }
        public string PremiseType { get; set; }
        public string ContractAccount { get; set; }
        public string ContractAccountType { get; set; }
        public string OwnerName { get; set; }
        public string UaeNational { get; set; }
        public string MoveInDate { get; set; }
        public string MoveOutDate { get; set; }
        public string MakaniNumber { get; set; }
        public string EidNumber { get; set; }
        public string SocialBenifit { get; set; }
        public string CommunityNumber { get; set; }
        public string BillingCycle { get; set; }
        public string RecInflationAllowance { get; set; }
        public DEWAInflationAddress Address { get; set; }
    }

    public class ADDCInflationReponse
    {
        public ADDCInflationData data { get; set; }
        public string StatusCode { get; set; }
        public string StatusMessage { get; set; }
    }
    public class AADCInflationReponse
    {
        public AADCInflationData data { get; set; }
        public string StatusCode { get; set; }
        public string StatusMessage { get; set; }
    }

    public class DEWAInflationResponse
    {
        public DEWAInflationData data { get; set; }
        public string StatusCode { get; set; }
        public string StatusMessage { get; set; }
    }
    public class EWEInflationData
    {
        public string EmiratesID { get; set; }
        public string AccountStatus { get; set; }
        public string AccountType { get; set; }
        public string UAENational { get; set; }
        public string BillingCycle { get; set; }
        public string ReceivingInflationAllowance { get; set; }
        public string StatusCode { get; set; }
        public string StatusMessage { get; set; }
    }

    public class EWEInflationResponse
    {
        public EWEInflationData data { get; set; }
        public string StatusCode { get; set; }
        public string StatusMessage { get; set; }
    }
    public class SEWAInflationResponse
    {
        public SEWAInflationData data { get; set; }
        public string StatusCode { get; set; }
        public string StatusMessage { get; set; }
    }
    public class SEWAInflationData
    {
        public string EmiratesId { get; set; }
        public string AccountStatus { get; set; }
        public string AccountType { get; set; }
        public string IsUAENational { get; set; }
        public string BillingCycle { get; set; }
        public string ReceivingInflationAllowance { get; set; }
        public string ResponseCode { get; set; }
        public string ResponseMessage { get; set; }
    }
    public class TAQAInflationResponse
    {
        public TAQADetails data { get; set; }
        public string StatusCode { get; set; }
        public string StatusMessage { get; set; }
    }
    public class TAQADetails
    {
        public string EID { get; set; }
        public string ACC_Status { get; set; }
        public string ACC_Type { get; set; }
        public string UAE_National { get; set; }
        public string Bill_Cycle { get; set; }
        public string receivingInflationallowances { get; set; }
        public string statuscode { get; set; }
        public string statusMessage { get; set; }
    }
}
