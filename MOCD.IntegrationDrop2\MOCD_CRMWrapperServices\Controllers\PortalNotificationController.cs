﻿using Microsoft.Xrm.Sdk;
using MOCD_CRMWrapperServices.Infrastructure;
using MOCD_CRMWrapperServices.Helper;
using MOCD_CRMWrapperServices.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace MOCD_CRMWrapperServices.Controllers
{
    [RoutePrefix("API/PortalNotification")]
    public class PortalNotificationController : ApiController
    {
        private static IOrganizationService service;
        public PortalNotificationController()
        {
            service = CRMConnection.GetCrmConnection();
        }

        [HttpGet, Route("GetPortalNofications")]
        public IHttpActionResult GetPortalNofications(Guid beneficiaryId)
        {
            var _request = PortalNotificationService.GetPortalNofications(beneficiaryId, service);
            return Ok(Responses.Success(_request));
        }


        [HttpPost, Route("ChangeNotificationsToRead")]
        public IHttpActionResult ChangeNotificationsToRead([FromBody] List<Guid> ids)
        {
            var _request = PortalNotificationService.ChangeNotificationsToRead(ids, service);
            return Ok(Responses.Success(_request));
        }
    }
}
