﻿using Microsoft.Xrm.Sdk;
using MOCD_CommonAPI.API_Configuration;
using MOCD_CommonAPI.Common;
using MOCD_CommonAPI.Company;
using MOCD_CommonAPI.Employee;
using MOCD_CRMWrapperServices.Infrastructure;
using MOCD_ExternalAPI.MoHREAPIs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace MOCD_CRMWrapperServices.Controllers
{
    [RoutePrefix("API/MoHRE")]
    public class MoHREController : ApiController
    {
        private static HttpClient client;
        private static IOrganizationService service;
        private static APIConfig CompanyAPIConfig;
        private static APIConfig JobAPIConfig;
        public MoHREController()
        {
            client = new HttpClient();
            client.Timeout = TimeSpan.FromSeconds(60);
            service = CRMConnection.GetCrmConnection();

            CompanyAPIConfig = APIConfiguration.GetAPIConfiguration(service, "Company_API");
            JobAPIConfig = APIConfiguration.GetAPIConfiguration(service, "Job_API");
        }

        [HttpPost, Route("GetCompanyDetails")]
        public async Task<IHttpActionResult> GetCompanyDetails(GetCompanyDetailsRequest request )
        {
            var result = await CompanyAPI.GetCompanyDetails(client, request,CompanyAPIConfig);
            return Ok(Responses.Success(result));
        }


        [HttpPost, Route("GetEmployeeDetails")]
        public async Task<IHttpActionResult> GetEmployeeDetails(GetEmployeeDetailsRequest request)
        {
            var result = await EmployeeAPI.GetEmployeeDetails(client, request, JobAPIConfig);
            return Ok(Responses.Success(result));
        }
    }
}
