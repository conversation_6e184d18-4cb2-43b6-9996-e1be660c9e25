﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Constant
{
    public static class CustomMessages
    {
        public readonly static string InvalidId = "The Id is invalid.";
        public readonly static string CaseId = "The case Id is invalid.";
        public readonly static string BeneficiaryId = "The beneficiary Id is invalid.";
        public readonly static string MobileNumber = "The mobile number is  invalid.";
        public readonly static string EmiratesId = "The emirates Id is invalid.";
        public readonly static string IncorrectEmiratesId = "The emirates Id is incorrect.";
        public readonly static string OTPCode = "The OTP code is invalid.";
        public readonly static string CasePerimission = "You do not have permission to access this case..";
        public readonly static string NotificationId = "The notification Id is  invalid.";
        public readonly static string TopicIdForFeedback = "could not find the topic Id in configuration table..";
    }
}
