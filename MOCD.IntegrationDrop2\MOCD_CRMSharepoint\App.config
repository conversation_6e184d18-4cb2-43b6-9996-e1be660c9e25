﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2" />
    </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.2" newVersion="6.0.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
	<appSettings>
		<add key="CrmConnectionString" value="AuthType=OAuth;Username=<EMAIL>;Password=gJuI9*41;Url= https://mocdpwcdev.crm15.dynamics.com;AppId=51f81489-12ee-4a9e-aaae-a2591f45987d; RedirectUri=app://58145B91-0C36-4500-8554-080854F2AC97;LoginPrompt=Never" />
		<add key="SPSiteUrl" value="https://mocdae.sharepoint.com/sites/mocdpwcdev" />
		<add key="SPUserName" value="<EMAIL>" />
		<add key="SPPassword" value="gJuI9*41" />
		<add key="CRMClientID" value="dfe64cd8-9292-481a-a52a-2a1f4554b497" />
		<add key="CRMClientSecret" value="****************************************" />
		<add key="CRMResourceUrl" value="https://mocdpwcdev.crm15.dynamics.com" />
		<add key="hexa_internalstatus" value="EF2C8FA5-A26C-ED11-81AC-0022480DA504" />
		<add key="SPBatchCount" value="10" />


		
	</appSettings>
</configuration>