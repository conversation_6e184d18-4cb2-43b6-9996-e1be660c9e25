﻿using Microsoft.SharePoint.Client;
using System.Configuration;
using System.Linq;

namespace MCRMSharepoint
{
    internal class SPHelper
    {
        static string SiteUrl;
        static string userName;
        static string password;

        internal static void readSPConfigurations()
        {

            SiteUrl = ConfigurationManager.AppSettings["SPSiteUrl"];
            userName = ConfigurationManager.AppSettings["SPUserName"];
            password = ConfigurationManager.AppSettings["SPPassword"];
        }

        public static void UploadDocument()
        {
            readSPConfigurations();
        }
        private static void CreateFolder(Web web, string Title, string folderName)
        {
            if (!FolderExists(web, Title, folderName))
            {
                var documentsList = web.Lists.GetByTitle(Title);
                var info = new ListItemCreationInformation();
                info.UnderlyingObjectType = FileSystemObjectType.Folder;
                info.LeafName = folderName.Trim();
                var newItem = documentsList.AddItem(info);
                newItem["Title"] = folderName;
                newItem.Update();
                web.Context.ExecuteQuery();
            }
        }

        private static bool FolderExists(Web web, string listTitle, string folderUrl)
        {
            var list = web.Lists.GetByTitle(listTitle);
            var folders = list.GetItems(CamlQuery.CreateAllFoldersQuery());
            web.Context.Load(list.RootFolder);
            web.Context.Load(folders);
            web.Context.ExecuteQuery();
            var folderRelativeUrl = string.Format("/{0}/{1}", list.RootFolder.Name, folderUrl);
            return Enumerable.Any(folders, folderItem => (string)folderItem["FileRef"] == folderRelativeUrl);
        }






    }


}
