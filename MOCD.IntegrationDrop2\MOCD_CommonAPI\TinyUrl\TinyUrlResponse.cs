﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.TinyUrl
{
    public class TinyUrlResponse
    {
        public Data data { get; set; }
        public int code { get; set; }
        public List<object> errors { get; set; }

        public class Analytics
        {
            public bool enabled { get; set; }
            public bool @public { get; set; }
        }

        public class Data
        {
            public string domain { get; set; }
            public string alias { get; set; }
            public bool deleted { get; set; }
            public bool archived { get; set; }
            public Analytics analytics { get; set; }
            public List<string> tags { get; set; }
            public DateTime created_at { get; set; }
            public DateTime expires_at { get; set; }
            public string tiny_url { get; set; }
            public string url { get; set; }
        }
    }
}
