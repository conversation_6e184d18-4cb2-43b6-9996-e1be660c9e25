﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace MOCD_CRMWrapperServices.Constants
{
    public static class MasterColumnNames
    {
        public const string Name = "mocd_name";
        public const string NameAR = "mocd_namear";
        public const string Code = "mocd_code";
    }

    public static class ProcessStatusColumnNames
    {
        public const string Name = "mocd_nameen";
        public const string NameAR = "mocd_namear";
    }

    public static class CaseColumns
    {
        public const string Name = "hexa_name";
        public const string HouseHoldTradeLicense = "mocd_householdtradelicense";
        public const string HouseHoldContributesToIncome = "mocd_householdcontributestoincome";
        public const string Occupation = "mocd_occupation";
        public const string AlternatePhone = "mocd_alternatephone";
        public const string Jobtitle = "mocd_jobtitle";
        public const string EidCardNumber = "mocd_eidcardnumber";
        public const string SubmissionTime = "hexa_submittedon";
        public const string Education = "mocd_education";
        public const string Accommodationtype = "mocd_accommodationtype";
        public const string IsHouseHoldHead = "mocd_ishouseholdhead";
        public const string Address = "mocd_address";
        public const string HouseHoldRetirementorPensionIncome = "mocd_householdretirementorpensionincome";
    }

    public static class CaseBeneficiaryIncome
    {
        public const string Name = "mocd_name";
        public const string IncomeType = "mocd_incometype";
        public const string IncomeBase = "mocd_income_base";
        public const string Income = "mocd_income";
        public const string CompanyName = "mocd_companyname";
        public const string IncomeSource = "mocd_incomesource";
        public const string Familybook = "mocd_casefamilybook";
    }

    public static class CaseBeneficiaryPension
    {
        public const string Name = "mocd_name";
        public const string PensionType = "mocd_pensiontype";
        public const string PensionAuthority = "mocd_pensionauthority";
        public const string PensionAmountBase = "mocd_pensionamount_base";
        public const string PensionAmount = "mocd_pensionamount";
        public const string Familybook = "mocd_casefamilybook";

    }

    public static class CaseBeneficiaryTradeLicense
    {
        public const string Name = "mocd_name";
        public const string Income = "mocd_income";
        public const string Familybook = "mocd_casefamilybook";

    }

    public static class FamilyBookColumns
    {
        public const string Name = "mocd_name";
        public const string Relationshiptofamilyhead = "mocd_relationshiptofamilyhead";
        public const string Familymemberretirementorpension = "mocd_familymemberretirementorpension";
        public const string Familymembercontributestoincome = "mocd_familymembercontributestoincome";
        public const string Familyheadtradelicense = "mocd_familyheadtradelicense";
        public const string Isinformationcomplete = "mocd_isinformationcomplete";
        public const string Dependent = "mocd_dependent";
    }

    public static class PortalNotificationColumns
    {
        public const string NotificationEn = "mocd_notificationen";
        public const string TitleEn = "mocd_name";
        public const string TitleAr = "mocd_titlear";
        public const string NotificationAr = "mocd_notificationar";
        public const string Beneficiary = "mocd_beneficiary";
        public const string StatusCode = "statuscode";
    }
}