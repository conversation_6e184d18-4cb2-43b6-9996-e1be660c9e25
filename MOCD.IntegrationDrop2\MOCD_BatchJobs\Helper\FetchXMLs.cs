﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_BatchJobs.Helper
{
    public static class FetchXMLs
    {
        public static string GetApprovedCasesXML(string internalStatus)
        {
            var fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                              <entity name='hexa_request'>
                                <attribute name='hexa_name' />
                                <attribute name='hexa_requestid' />
                                <attribute name='hexa_internalstatus' />
                                <attribute name='mocd_emiratesid' />
                                <attribute name='hexa_portalcontact' />
                                <filter type='and'>
                                   <condition attribute='hexa_internalstatus' operator='eq' value='" + internalStatus + @" '/>
                                   <condition attribute='hexa_processtemplate' operator='in'>
                                     <value uiname='Social Aid - New Beneficiary' uitype='hexa_processtemplate'>{FDF382C5-E821-415D-BD8A-F6476EBAB0DC}</value>
                                     <value uiname='Social Aid - Existing Beneficiary' uitype='hexa_processtemplate'>{309404BB-3446-4CB1-89E0-6BC95EE6C932}</value>
                                   </condition> 
                                </filter>
                              </entity>
                            </fetch>";

            return fetchxml;
        }

        public static string GetFamilyBookApprovedCasesXML()
        {
            var fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                              <entity name='mocd_casefamilybook'>
                                <attribute name='mocd_casefamilybookid' />
                                <attribute name='mocd_name' />
                                <attribute name='mocd_dependent' />
                                <order attribute='mocd_name' descending='false' />
                                <link-entity name='contact' from='contactid' to='mocd_dependent' link-type='inner' alias='dep'>
    	                            <attribute name='mocd_emiratesid' />   
                                </link-entity>
                                <link-entity name='hexa_request' from='hexa_requestid' to='mocd_case' link-type='inner' alias='ad'>
                                  <filter type='and'>
                                    <condition attribute='hexa_internalstatus' operator='eq' uiname='Eligible / Approved' uitype='hexa_processstatustemplate' value='{EF2C8FA5-A26C-ED11-81AC-0022480DA504}' />
                                    <condition attribute='hexa_processtemplate' operator='in'>
                                        <value uiname='Social Aid - New Beneficiary' uitype='hexa_processtemplate'>{FDF382C5-E821-415D-BD8A-F6476EBAB0DC}</value>
                                        <value uiname='Social Aid - Existing Beneficiary' uitype='hexa_processtemplate'>{309404BB-3446-4CB1-89E0-6BC95EE6C932}</value>
                                   </condition> 
                                  </filter>
                                </link-entity>
                              </entity>
                            </fetch>";

            return fetchxml;
        }
    }
}
