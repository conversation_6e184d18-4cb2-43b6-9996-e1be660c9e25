﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.AccountDetails.Response
{
    public class AccountDetail
    {
        public string EmiratesID { get; set; }
        public string BusinessPartnerNo { get; set; }
        public string ContractAccountNo { get; set; }
        public string Category { get; set; }
        public string AccountClass { get; set; }
        public string SpecialCode { get; set; }
        public string SpecialNeed { get; set; }
        public string PremiseNo { get; set; }
        public object MoveInDate { get; set; }
        public object MoveOutDate { get; set; }
        public object PE_StartingDate { get; set; }
        public string Email { get; set; }
        public string MobileNo { get; set; }
        public string Latitude_E { get; set; }
        public string Longitude_E { get; set; }
        public string Latitude_W { get; set; }
        public string Longitude_W { get; set; }
        public AddressDetails AddressDetails { get; set; }
        public LatestInvoice LatestInvoice { get; set; }
    }

    public class AddressDetails
    {
        public string Emirate { get; set; }
        public string FloorNo { get; set; }
        public string HouseNo { get; set; }
        public string BuildingName { get; set; }
        public string Landmark { get; set; }
    }

    public class LatestInvoice
    {
        public object Date { get; set; }
        public string Base64Data_PDF { get; set; }
    }

    public class AccountDeatilsResponse
    {
        public List<AccountDetail> AccountDetails { get; set; }
    }
}
