﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Request
{
    public class Request
    {
        public Guid IdRequest { get; set; }
        public Guid IdContact { get; set; }
        public Guid IdProcessTemplate { get; set; }
        public Guid IdAllowanceCategory { get; set; }
    }

    public class RequestResponse
    {
        public string RequestName { get; set; }
        public DateTime CreatedDate { get; set; }
        public PortalContact Contact { get; set; }
        public ProcessTemplate Template { get; set; }
        public StatusDetails Status { get; set; }
        public Guid CaseId { get; set; }
        public Guid EmirateId { get; set; }
        public bool EligibleForAppeal { get; set; }
        public bool EligibleForEdit { get; set; }
        public bool IsNomiatedInflationCase { get; set; } = false;
    }

    public class StatusDetails
    {
        public string Key { get; set; }
        public string Value { get; set; }
    }

    public class PortalContact
    {
        public string ContactId { get; set; }
        public string FirstName { get; set; }
        public string FirstNameAr { get; set; }
        public string LastName { get; set; }
        public string LastNameAr { get; set; }
        public string FullNameAr { get; set; }
        public string FullNameEn { get; set; }
        public string EmirateID { get; set; }
        public string Email { get; set; }
        public string MobileNumber { get; set; }
    }

    public class ProcessTemplate
    {
        public string TemplateId { get; set; }
        public string TemplateName { get; set; }
        public string TemplateNameAr { get; set; }
    }

    public class AllowanceTransation
    {
        public string Name { get; set; }
        public DateTime PayDate { get; set; }
        public string TotalAmount { get; set; }
        public string RequestName { get; set; }
        public PortalContact Contact { get; set; }
    }
}
