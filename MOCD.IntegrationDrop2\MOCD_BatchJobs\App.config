﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<connectionStrings>
		<add name="connectionString" connectionString="AuthType=Office365;Url=https://mocdpreprod.crm15.dynamics.com/main.aspx;Username=<EMAIL>; Password=wvg$$VEU" />
	</connectionStrings>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
	</startup>
	<appSettings>

		<!--Client's Dev To be removed before publishing to the production-->
		<add key="ClientId" value="f562ab65-596e-45f7-904b-5b55be1181de" />
		<add key="ClientSecret" value="****************************************" />
		<add key="Resource" value="https://mocdcrm.crm15.dynamics.com/" />

		<!--<add key="ClientId" value="dfe64cd8-9292-481a-a52a-2a1f4554b497" />
		<add key="ClientSecret" value="****************************************" />
		<add key="Resource" value="https://mocdpwcdev.crm15.dynamics.com/" />-->

		<!--PwC Dev To be removed before publishing to the production-->
		<!--<add key="ClientId" value="fffa45b7-75c6-4249-8d4c-8272a8062bfb" />
		<add key="ClientSecret" value="****************************************" />
		<add key="Resource" value="https://mocddev.crm15.dynamics.com" />-->

	</appSettings>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.2" newVersion="6.0.0.2" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Xrm.Sdk" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration>