﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Job
{
    public  class JobDetailsResponse
    {
        public bool succeeded { get; set; }
        public List<JobDetails> data { get; set; }
        public List<object> errors { get; set; }
    }

    public class JobDetails
    {
        public string name { get; set; }
        public string customerSegment { get; set; }
        public List<object> customerType { get; set; }
        public string nationality { get; set; }
        public string emiratesId { get; set; }
        public string gender { get; set; }
        public DateTime dataOfBirth { get; set; }
        public LastEmployment lastEmployment { get; set; }
        public TotalServicePeriod totalServicePeriod { get; set; }
    }

    public class LastEmployment
    {
        public string employerName { get; set; }
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
        public string jobTitle { get; set; }
        public double salary { get; set; }
    }
    public class TotalServicePeriod
    {
        public int days { get; set; }
        public int months { get; set; }
        public int years { get; set; }
    }
}
