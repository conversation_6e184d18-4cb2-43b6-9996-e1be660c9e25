﻿using MOCD_CommonAPI.Common;
using System;
using System.Collections.Generic;
using System.IO;

namespace MOCD_CommonAPI.Request
{

    /// <summary>
    /// Request payload to update the request
    /// </summary>
    public class CasePatchRequest
    {
        public string UpdateType { get; set; } //CREATE,DRAFT,SUBMIT
        public string reOpenReason { get; set; }
        public int CaseType { get; set; }
        public Guid IdCase { get; set; }
        public Guid ParentCaseId { get; set; }
        public Case CaseDetails { get; set; }
        public Guid IdProcessTempalte { get; set; }
        public Guid IdAllowanceCategory { get; set; }
        public Guid IdBeneficiary { get; set; }
        public int Index { get; set; }
        public int SubIndex { get; set; }
    }

    /// <summary>
    /// Case details
    /// </summary>
    public class Case
    {
        public Guid Occupation { get; set; }
        public String JobTitle { get; set; }
        public string Alternativenumber { get; set; }
        public string Address { get; set; }
        public string CaseRef { get; set; }
        public Guid ProcessTemplate { get; set; }
        public string AlternativeEmail { get; set; }
        public Guid Education { get; set; }
        public Guid MaritalStatus { get; set; }
        public Guid AccomodationType { get; set; }
        public Guid Emirate { get; set; }
        public Guid Area { get; set; }
        public Guid Category { get; set; }
        public Guid SubCategory { get; set; }
        public Guid Center { get; set; }
        public bool RegisteredWithEWE { get; set; }
        public string EWEBill { get; set; }
        public string EWEAccountNumber { get; set; }
        public string RelatedEmiratesID { get; set; }
        public bool ReceiveSocialAid { get; set; }
        public int? EntityReceivedFrom { get; set; }
        public bool ReceiveInflationAllowance { get; set; }
        public bool IsHouseholdHeadContributeToIncome { get; set; }
        public List<IncomeSourceDetails> ListIncomeSourceDetails { get; set; }
        public bool IsHouseholdHeadReceivePensionIncome { get; set; }
        public List<PensionDetails> ListPensionDetails { get; set; }
        public bool IshouseholdHeadTradeLicense { get; set; }
        public List<TradeLicenseDetails> ListTradeLicenseDetails { get; set; }
        public bool IshouseholdHeadReceiveRentalIncome { get; set; }
        public List<RentalDetails> ListRentalDetails { get; set; }
        public List<FamilyMemberDetails> ListFamilyMember { get; set; }
        public List<ChildrenDetails> ListofChildren { get; set; }
        public bool? IsActiveStudent { get; set; }
        public int? Terminated { get; set; }
        public int? MilitaryServiceStatus { get; set; }
        public bool? DraftedinMilitaryService { get; set; }
        public bool? PursuingHigherEducation { get; set; }
        public long? ReceivedLocalSupport { get; set; }
        public string GuardianEmiratesID { get; set; }
        public Guid PortalPersona { get; set; }
        public Guid SubPersona { get; set; }
        public string ChildEligibilityforWomeninDifficulty { get; set; }
        public int? NumberOfChildren { get; set; }
        public int? NumberOfChildrenLessThan25 { get; set; }
        public List<string> ListUploadedDocuments { get; set; }
        public string localSocialSupportEntities { get; set; }
        public bool? ApplyHousingAllowance { get; set; }
        public bool? IsHouseholOwnerResidentialProperty { get; set; }
        public bool? IsUtilityBillIssuedForFullyOwnedProperty { get; set; }
        public Guid LivingSituation { get; set; }
        public long? ReceivingFederalLocalhousingsupport { get; set; }
        public bool? ReceivingHousingAllowanceFromEmployer { get; set; }
        public bool? ReceivesHousingSupportFromHusband { get; set; }
        public bool? FullOwnershipResidentialProperty { get; set; }
        public bool? ApplyEducationAllowance { get; set; }
        public List<EducationCase> EducationCaseDetails { get; set; }
        public bool? ApplyInflationAllowance { get; set; }
        public bool? ApplyUtilityAllowance { get; set; }
        public int? UtilityProvider { get; set; }
        public string UtilityAccountNumber { get; set; }
        public Guid InflationCategory { get; set; }
        public int? InflationType { get; set; }
        public long? LegacyCaseType { get; set; }
        public bool? CaseForPODChild { get; set; }
    }

    /// <summary>
    /// Case details
    /// </summary>
    public class CasePersonalDetails
    {
        public Guid IdCase { get; set; }
        public Guid Occupation { get; set; }
        public String JobTitle { get; set; }
        public string Alternativenumber { get; set; }
        public string Address { get; set; }
        public string CaseRef { get; set; }
        public string AlternativeEmail { get; set; }
        public Guid Education { get; set; }
        public Guid MaritalStatus { get; set; }
        public Guid AccomodationType { get; set; }
        public Guid Emirate { get; set; }
        public Guid Area { get; set; }
    }

    /// <summary>
    /// Case details
    /// </summary>
    public class CaseFamilyHeadDetails
    {
        public Guid IdCase { get; set; }
        public bool IsHouseholdHeadContributeToIncome { get; set; }
        public List<IncomeSourceDetails> ListIncomeSourceDetails { get; set; }
        public bool IsHouseholdHeadReceivePensionIncome { get; set; }
        public List<PensionDetails> ListPensionDetails { get; set; }
        public bool IshouseholdHeadTradeLicense { get; set; }
        public List<TradeLicenseDetails> ListTradeLicenseDetails { get; set; }
        public bool IshouseholdHeadReceiveRentalIncome { get; set; }
        public List<RentalDetails> ListRentalDetails { get; set; }
    }

    /// <summary>
    /// Case details
    /// </summary>
    public class CaseFamilyMembersDetails
    {
        public bool IsUpdate { get; set; }
        public Guid IdCase { get; set; }
        public string FamilyHeadNameAR { get; set; }
        public string FamilyHeadNameEN { get; set; }
        public string KhulasitQaidNumber { get; set; }
        public List<FamilyMemberDetails> ListFamilyMember { get; set; }
        public List<ChildrenDetails> ListofChildren { get; set; }
    }

    /// <summary>
    /// Income source details
    /// </summary>
    public class IncomeSourceDetails
    {
        public Guid IdIncome { get; set; }
        public Guid IncomeSource { get; set; }
        public decimal IncomeAmount { get; set; }
        public string CompanyName { get; set; }
    }

    /// <summary>
    /// Pension income details
    /// </summary>
    public class PensionDetails
    {
        public Guid IdPension { get; set; }
        public Guid PensionType { get; set; }
        public Guid PensionAuthority { get; set; }
        public decimal IncomeAmount { get; set; }
    }

    /// <summary>
    /// Trade license income details
    /// </summary>
    public class TradeLicenseDetails
    {
        public Guid IdTradeLicense { get; set; }
        public decimal IncomeAmount { get; set; }
    }

    /// <summary>
    /// Rental income details
    /// </summary>
    public class RentalDetails
    {
        public Guid IdRental { get; set; }
        public decimal IncomeAmount { get; set; }
        public string ContractNumber { get; set; }
        public string ContractStartDate { get; set; }
        public string ContractEndDate { get; set; }
        public Guid RentalSource { get; set; }
    }

    /// <summary>
    /// Family details
    /// </summary>
    public class FamilyMemberDetails
    {
        public Guid Id { get; set; }
        public Guid IdDependentBeneficary { get; set; }
        public bool IsInformationUpdated { get; set; }
        public string Fullname { get; set; }
        public string FullnameAR { get; set; }
        public string FullnameEN { get; set; }
        public Guid Relationship { get; set; }
        public bool IsFamilyMemberContributeToIncome { get; set; }
        public List<IncomeSourceDetails> ListIncomeSourceDetails { get; set; }
        public bool IsFamilyMemberReceivePensionIncome { get; set; }
        public List<PensionDetails> ListPensionDetails { get; set; }
        public bool IsFamilyMemberReceiveTradeLicense { get; set; }
        public List<TradeLicenseDetails> ListTradeLicenseDetails { get; set; }
        public bool IsFamilyMemberReceiveRentalIncome { get; set; }
        public List<RentalDetails> ListRentalDetails { get; set; }
        public string FamilyHeadEN { get; set; }
        public string FamilyHeadAR { get; set; }
        public string KhulasitQaidNumber { get; set; }
        public Guid Occupations { get; set; }

    }

    public class ChildrenDetails
    {
        public Guid Id { get; set; }
        public Guid IdDependentBeneficary { get; set; }
        public string FullNameAR { get; set; }
        public string FullNameEN { get; set; }
        public bool? IsPursuingHigherEducation { get; set; }
        public bool? IsDraftedinMilitaryService { get; set; }
        public Guid Occupations { get; set; }
        public string Age { get; set; }
    }

    /// <summary>
    /// Patch case response
    /// </summary>
    public class CasePatchResponse
    {
        public Guid IdCase { get; set; }
        public Lookup Status { get; set; }
        public String SubmittedOn { get; set; }
        public string CaseRef { get; set; }
        public string Eid { get; set; }
        public string EmailAddress { get; set; }
        public Lookup Beneficiary { get; set; }
    }

    /// <summary>
    /// Response of patch API
    /// </summary>
    public class PatchCaseResponse
    {
        public Guid IdCase { get; set; }
        public string NameEn { get; set; }
        public string NameAr { get; set; }
        public string EmiratesId { get; set; }
        public string Email { get; set; }
        public string MobileNumber { get; set; }
        public string SubmittedOn { get; set; }
        public string CaseRef { get; set; }
        public List<FamilyMemberDetails> ListFamilyMember { get; set; }
    }

    public class GetCaseRequestByCaseIdRequest
    {
        public Guid CaseId { get; set; }
        public Guid BeneficiaryId { get; set; }
        public int Index { get; set; }
        public int SubIndex { get; set; }
    }

    public class EducationCase
    {
        public Guid IdCaseBeneficiaryEducationalAllowance { get; set; }
        public string FullNameAr { get; set; }
        public string FullNameEn { get; set; }
        public bool? ApplyEducationAllowance { get; set; }
        public bool? childCompletedSemesterInUniversity { get; set; }
        public int? highSchoolCurriculuim { get; set; }
        public int? enrolledEducationStream { get; set; }
        public int? EmSATorAdvancedPlacementScores { get; set; }
        public bool? IsCompletedFromPortal { get; set; }
        public string Age { get; set; }
        public Guid IdChild { get; set; }
        public bool? IsEnrolledInNationalService { get; set; }
        public double? cgpa { get; set; }
        public double? creditHours { get; set; }
        public Guid universityName { get; set; }
        public string unaccreditedUniversityName { get; set; }
    }

    public class CaseRefundInstallments
    {
        public Guid Id { get; set; }
        public int? Status { get; set; }
        public decimal? Amount { get; set; }
        public decimal? reClaimAmount { get; set; }
        public DateTime? DueDate { get; set; }
        public string TransactionID { get; set; }
    }
}
