﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Tenancy
{
    
    public class TenancyDetailsResponse
    {
        public int code { get; set; }
        public List<TenancyDetails> Data { get; set; } = new List<TenancyDetails>();
        public string message { get; set; }
    }

    public class TenancyDetails
    {
        public string area { get; set; }
        public string lanD_NO { get; set; }
        public DateTime conT_START_DATE { get; set; }
        public int zonE_CODE { get; set; }
        public string flaT_DESC { get; set; }
        public string conT_TYPE { get; set; }
        public string uaE_NATIONAL_ID { get; set; }
        public int contracT_NO { get; set; }
        public DateTime conT_END_DATE { get; set; }
        public string nationaL_NO { get; set; }
        public string passporT_NO { get; set; }
        public string tenanT_NAME { get; set; }
        public string status { get; set; }

    }


}
