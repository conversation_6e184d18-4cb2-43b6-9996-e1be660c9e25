﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Employee
{
    public class GetEmployeeDetailsRequest
    {
        public string eida { get; set; }
        public string passport { get; set; }
        public string dateOfBirth { get; set; }
    }

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class EmployeeDetailsResponse
    {
        public string emiratesIDNumber { get; set; }
        public string employeeNameEnglish { get; set; }
        public string employeeNameArabic { get; set; }
        public string companyCode { get; set; }
        public string companyNameEnglish { get; set; }
        public string companyNameArabic { get; set; }
        public string salary { get; set; }
        public string contractStartDate { get; set; }
        public string contractEndDate { get; set; }
        public string SuccessFlag { get; set; }
        public string ErrorFlag { get; set; }
        public string ErrorDescEnglish { get; set; }
        public string ErrorDescArabic { get; set; }
    }


}
