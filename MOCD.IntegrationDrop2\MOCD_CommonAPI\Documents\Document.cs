﻿using MOCD_CommonAPI.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Documents
{
    /// <summary>
    /// List of documents with segregation of additinal and personal documents 
    /// </summary>
    public class Document
    {
        public List<DocumentDetails> ListAdditionalDoc { get; set; }
        public List<DocumentDetails> ListPersonalDocs { get; set; }
    }

    /// <summary>
    /// Document details with list of attachments
    /// </summary>
    public class DocumentDetails
    {
        public Guid IdDocuments { get; set; }
        public string NameEn { get; set; }
        public string NameAr { get; set; }
        public bool IsOptional { get; set; }
        public string Comments { get; set; }
        public Status Status { get; set; }
        public List<AttachmentDetails> ListAttachments { get; set; }
        public bool IsCreatedFromPortal { get; set; }
    }

    /// <summary>
    /// Attachment deatils 
    /// </summary>
    public class AttachmentDetails
    {
        public Guid Id { get; set; }
        public string MimeType { get; set; }
        public string FileName { get; set; }
        public string Size { get; set; }
        public string AttachmentBody { get; set; }
    }

    /// <summary>
    /// Request body for get all documents for a request
    /// </summary>
    public class AllDocumentRequest
    {
        public Guid IdRequest { get; set; }
        public String Eid { get; set; }
    }


    /// <summary>
    /// Request body to create attachment
    /// </summary>
    public class postAttachmentRequest
    {
        public Guid idDocument { get; set; }
        public List<AttachmentDetails> listAttachments { get; set; }
    }
    public class requestDocumentObject
    {
        public string processDocumentTemplateGUID { get; set; }
        public string requestGUID { get; set; }
    }
}
