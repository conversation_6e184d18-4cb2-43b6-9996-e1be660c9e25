﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net472" />
  <package id="Microsoft.Azure.Storage.Blob" version="11.1.7" targetFramework="net472" />
  <package id="Microsoft.Azure.Storage.Common" version="11.1.7" targetFramework="net472" />
  <package id="Microsoft.Azure.WebJobs" version="3.0.18" targetFramework="net472" />
  <package id="Microsoft.Azure.WebJobs.Core" version="3.0.18" targetFramework="net472" />
  <package id="Microsoft.Azure.WebJobs.Extensions" version="4.0.1" targetFramework="net472" />
  <package id="Microsoft.Azure.WebJobs.Host.Storage" version="4.0.1" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.CrmSdk.CoreAssemblies" version="********" targetFramework="net472" />
  <package id="Microsoft.CrmSdk.Deployment" version="********" targetFramework="net472" />
  <package id="Microsoft.CrmSdk.Workflow" version="********" targetFramework="net472" />
  <package id="Microsoft.CrmSdk.XrmTooling.CoreAssembly" version="********" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.EnvironmentVariables" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.FileExtensions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Json" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Physical" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileSystemGlobbing" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Hosting" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Configuration" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options.ConfigurationExtensions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Clients.ActiveDirectory" version="3.19.8" targetFramework="net472" />
  <package id="Microsoft.Rest.ClientRuntime" version="2.3.20" targetFramework="net472" />
  <package id="Microsoft.Web.WebJobs.Publish" version="17.1.361" targetFramework="net472" />
  <package id="Microsoft.WindowsAzure.ConfigurationManager" version="3.2.3" targetFramework="net472" />
  <package id="ncrontab.signed" version="3.3.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="4.4.0" targetFramework="net472" />
  <package id="System.Diagnostics.TraceSource" version="4.3.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="6.0.0" targetFramework="net472" />
  <package id="System.Text.Json" version="6.0.2" targetFramework="net472" />
  <package id="System.Threading.Tasks.Dataflow" version="4.8.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
</packages>