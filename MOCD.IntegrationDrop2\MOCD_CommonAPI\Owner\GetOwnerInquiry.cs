﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static MOCD_CommonAPI.TinyUrl.TinyUrlResponse;

namespace MOCD_CommonAPI.Owner
{

    public class GetOwnerInquiryRequest
    {
        public string OwnerNameEn { get; set; }
        public string OwnerNameAr { get; set; }
        public string OwnerFamilyNameEn { get; set; }
        public string OwnerFamilyNameAr { get; set; }
        public string OwnerMobile { get; set; }
        public string OwnerCountryID { get; set; }
        public string OwnerPassportNo { get; set; }
        public string OwnerEmirateId { get; set; }
    }

    public class OwnerInquiryResponse
    {
        public List<OwnerInquiry> Data { get; set; } = new List<OwnerInquiry>();
        public int Status { get; set; }
    }


    public class OwnerInquiry
    {
        public string id { get; set; }
        public int CountryId { get; set; }
        public string CountryNameAr { get; set; }
        public string CountryNameEn { get; set; }
        public int DBAction { get; set; }
        public string EmiratesId { get; set; }
        public string FamilyNameAr { get; set; }
        public string FamilyNameEn { get; set; }
        public int Identity { get; set; }
        public string MobileNumber { get; set; }
        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public string OwnerNo { get; set; }
        public string PassportNo { get; set; }
    }



}
