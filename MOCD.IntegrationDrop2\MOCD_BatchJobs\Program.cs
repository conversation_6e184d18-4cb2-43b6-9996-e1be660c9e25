﻿using Microsoft.Extensions.Hosting;
using Microsoft.Xrm.Sdk;
using MOCD_BatchJobs.BatchJobBusiness;
using MOCD_BatchJobs.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_BatchJobs
{
    public class Program
    {
        static void Main(string[] args)
        {
            MainTask().Wait();
        }

        static async Task MainTask()
        {
            try
            {
                IOrganizationService service = CRMConnection.GetCrmConnection();

                //FamilyBookBusinessDetails.GetApiConfiguration(service);
                Console.WriteLine(DateTime.Now.ToString());
                PODMonthlyBatchJobService.FillDisabilityInformations(service);
                Console.WriteLine(DateTime.Now.ToString());
                Console.ReadLine();
            }
            catch (Exception ex) 
            {
                Console.WriteLine(ex.Message.ToString());
            }
        }
    }
}
