﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using MOCD_BatchJobs.Assets;
using MOCD_CommonAPI.API_Configuration;
using MOCD_CommonAPI.Common;
using MOCD_CRMWrapperServices.Services;
using MOCD_ExternalAPI.FamilyInformationAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_BatchJobs.BatchJobBusiness
{
    public class FamilyBookBusinessDetails
    {
        public static void GetApiConfiguration(IOrganizationService service)
        {
            string fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                  <entity name='mocd_apiconfiguration'>
                                    <attribute name='mocd_apiconfigurationid' />
                                    <attribute name='mocd_name' />
                                    <attribute name='mocd_sequence' />
                                    <attribute name='mocd_lastrunat' />
                                    <order attribute='mocd_name' descending='false' />
                                    <filter type='and'>
                                      <condition attribute='mocd_isscheduledjob' operator='eq' value='1' />
                                    </filter>
                                  </entity>
                                </fetch>";

            EntityCollection records = service.RetrieveMultiple(new FetchExpression(fetchxml));

            if (records.Entities != null && records.Entities.Count > 0)
            {
                int[] _sequence = new int[records.Entities.Count];
                int[] _sequence2 = new int[records.Entities.Count];
                int count = 0;
                Guid[] _guids = new Guid[records.Entities.Count];
                string[] _keyValue = new string[records.Entities.Count];

                foreach (var item in records.Entities)
                {
                    _sequence[count] = item.Contains("mocd_sequence") ? item.GetAttributeValue<int>("mocd_sequence") : 0;
                    _keyValue[count] = item.Contains("mocd_name") ? item.GetAttributeValue<string>("mocd_name") : string.Empty;
                    _guids[count] = item.Id;
                    count++;
                }

                _sequence2 = _sequence;

                Array.Sort(_sequence, _keyValue);
                Array.Sort(_sequence2, _guids);

                FamilyBookDetailsBusiness(service, _sequence, _keyValue, _guids);
            }
        }

        public static async Task FamilyBookDetailsBusiness(IOrganizationService service, int[] _sequence, string[] _keyValues, Guid[] _guids)
        {
            var duplicates = _sequence.GroupBy(x => x)
              .Where(g => g.Count() > 1)
              .Select(y => y.Key)
              .ToList();

            string fetchxml = @"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                  <entity name='contact'>
                                    <attribute name='fullname' />
                                    <attribute name='mocd_emiratesid' />
                                    <attribute name='contactid' />
                                    <order attribute='fullname' descending='false' />
                                    <filter type='and'>
                                      <condition attribute='statecode' operator='eq' value='0' />
                                    </filter>
                                  </entity>
                                </fetch>";

            EntityCollection records = service.RetrieveMultiple(new FetchExpression(fetchxml));

            if (records.Entities != null && records.Entities.Count > 0)
            {
                foreach (Entity entity in records.Entities)
                {
                    string _emiratesId = entity.Contains("mocd_emiratesid") ? entity.GetAttributeValue<string>("mocd_emiratesid") : string.Empty;
                    if (!string.IsNullOrEmpty(_emiratesId))
                    {
                        if (duplicates.Count == 0)
                        {
                            for (int i = 0; i < _sequence.Length; i++)
                            {

                                switch (_keyValues[i])
                                {
                                    case "familybook":
                                        FamilyBookService.GetFamilyBookDetails_ExternalV2(service, _emiratesId);
                                        break;
                                    case "icaPersonalDetails":
                                        APIConfig _config = APIConfiguration.GetAPIConfiguration(service, "icaPersonalDetails");
                                        var icaInfo = await PersonalInfoAPI.GetPersonalInformation(_emiratesId, _config);
                                        Task.WaitAll();
                                        if (icaInfo != null)
                                            ContactService.ICAPersonalInformation_BatchJob(icaInfo, service, _emiratesId);
                                        break;
                                    case "validateOtp":
                                        //var contact = await CunsumeOTPService.ValidateOTP(validateOTP, service);
                                        break;
                                    case "generateOtp":
                                        //var otp = await CunsumeOTPService.GenerateOTP(_emiratesId);
                                        break;
                                    case "GetDisabilityInformationByIDAPI":
                                        PODMonthlyBatchJobService.FillDisabilityInformations(service);
                                        break;
                                }
                            }
                        }
                        else
                        {
                            var tasks = new List<Task>();

                            for (int i = 0; i < _sequence.Length; i++)
                            {
                                if (duplicates.Contains(_sequence[i]))
                                {
                                    _ = Task.Run(async () =>
                                    {
                                        switch (_keyValues[i])
                                        {
                                            case "familybook":
                                                FamilyBookService.GetFamilyBookDetails_ExternalV2(service, _emiratesId);
                                                break;
                                            case "icaPersonalDetails":
                                                APIConfig _config = APIConfiguration.GetAPIConfiguration(service, "icaPersonalDetails");
                                                var icaInfo = await PersonalInfoAPI.GetPersonalInformation(_emiratesId, _config);
                                                Task.WaitAll();
                                                if (icaInfo != null)
                                                    ContactService.ICAPersonalInformation_BatchJob(icaInfo, service, _emiratesId);
                                                break;
                                            case "validateOtp":
                                                //var contact = await CunsumeOTPService.ValidateOTP(validateOTP, service);
                                                break;
                                            case "generateOtp":
                                                //var otp = await CunsumeOTPService.GenerateOTP(_emiratesId);
                                                break;
                                            case "GetDisabilityInformationByIDAPI":
                                                PODMonthlyBatchJobService.FillDisabilityInformations(service);
                                                break;
                                            default:
                                                break;
                                        }
                                    });
                                }
                                else
                                {
                                    switch (_keyValues[i])
                                    {
                                        case "familybook":
                                            FamilyBookService.GetFamilyBookDetails_ExternalV2(service, _emiratesId);
                                            break;
                                        case "icaPersonalDetails":
                                            APIConfig _config = APIConfiguration.GetAPIConfiguration(service, "icaPersonalDetails");
                                            var icaInfo = await PersonalInfoAPI.GetPersonalInformation(_emiratesId, _config);
                                            Task.WaitAll();
                                            if (icaInfo != null)
                                                ContactService.ICAPersonalInformation_BatchJob(icaInfo, service, _emiratesId);
                                            break;
                                        case "validateOtp":
                                            //var contact = await CunsumeOTPService.ValidateOTP(validateOTP, service);
                                            break;
                                        case "generateOtp":
                                            //var otp = await CunsumeOTPService.GenerateOTP(_emiratesId);
                                            break;
                                        case "GetDisabilityInformationByIDAPI":
                                            PODMonthlyBatchJobService.FillDisabilityInformations(service);
                                            break;
                                    }
                                }
                            }
                            var results = Task.WhenAll(tasks);
                        }
                    }
                }

                mocd_apiconfiguration _obj = new mocd_apiconfiguration();

                for (int i = 0; i < _sequence.Length; i++)
                {

                    switch (_keyValues[i])
                    {
                        case "familybook":
                            _obj = new mocd_apiconfiguration();
                            _obj.Id = _guids[i];
                            _obj.mocd_LastRunAt= DateTime.Now;
                            service.Update(_obj);

                            break;
                        case "icaPersonalDetails":
                            _obj = new mocd_apiconfiguration();
                            _obj.Id = _guids[i];
                            _obj.mocd_LastRunAt = DateTime.Now;
                            service.Update(_obj);

                            break;
                        case "validateOtp":
                            _obj = new mocd_apiconfiguration();
                            _obj.Id = _guids[i];
                            _obj.mocd_LastRunAt = DateTime.Now;
                            service.Update(_obj);

                            break;
                        case "generateOtp":
                            _obj = new mocd_apiconfiguration();
                            _obj.Id = _guids[i];
                            _obj.mocd_LastRunAt = DateTime.Now;
                            service.Update(_obj);

                            break;
                    }
                }
            }
        }
    }
}
