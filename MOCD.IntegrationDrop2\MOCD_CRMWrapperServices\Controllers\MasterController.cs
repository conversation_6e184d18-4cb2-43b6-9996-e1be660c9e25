﻿using Microsoft.Xrm.Sdk;
using MOCD_CRMWrapperServices.Infrastructure;
using MOCD_CRMWrapperServices.Helper;
using MOCD_CRMWrapperServices.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace MOCD_CRMWrapperServices.Controllers
{
    [RoutePrefix("API/Master")]
    public class MasterController : ApiController
    {
        private static IOrganizationService service;
        public MasterController()
        {
            service = CRMConnection.GetCrmConnection();
        }

        [HttpGet, Route("MasterData")]
        public IHttpActionResult MasterData()
        {
            var occupations = MasterService.GetOccupations(service);
            var maritalStatus = MasterService.GetMaritalStatus(service);
            var educations = MasterService.GetEducations(service);
            var accomadations = MasterService.GetAccomodations(service);
            var incomeSources = MasterService.GetIncomeSource(service);
            var tradeSources = MasterService.GetTradeSource(service);
            var pensionAuthority = MasterService.GetPensionAuthority(service);
            var pensionTypes = MasterService.GetPensionTypes(service);
            var familyRelationship = MasterService.GetFamilyRelationships(service);
            var rentalSource = MasterService.GetRentalSource(service);
            var caseStatus = MasterService.GetProcessStatus(service);
            var emirates = MasterService.GetEmirates(service);
            var areas = MasterService.GetAreas(service);
            var processtemplates = MasterService.GetProcessTemplates(service);
            var portalCategory = MasterService.GetPortalCategory(service);
            var subCategory = MasterService.GetSubCategory(service);
            var center = MasterService.GetCenters(service);
            var documentProcessTemplate = MasterService.GetdocumentProcessTemplates(service);
            var otherentities = MasterService.GetOtherEntities(service);
            var complaintTopics = MasterService.GetComplaintTopic(service);
            var complaintServices = MasterService.GetServices(service);
            var complaintSubServices = MasterService.GetSubServices(service);
            var portalPersona = MasterService.GetPortalPersona(service);
            var subPersona = MasterService.GetSubPersona(service);
            var inflationCategory = MasterService.GetInflationCategory(service);
            var installmentRates = MasterService.GetInstallmentRates(service);
            var reasonToEditInflation = MasterService.GetReasonsToEditInflation(service);
            var reasonToEditSwp = MasterService.GetReasonsToEditSwp(service);
            var Universities = MasterService.GetUniversities(service);
            ////////////////////////to be uncommented on to whom it may concern//////////////
            var masterData = new
            {
                Occupations = occupations,
                MaritalStatus = maritalStatus,
                Educations = educations,
                accomadations = accomadations,
                IncomeSources = incomeSources,
                TradeSources = tradeSources,
                PensionType = pensionTypes,
                PensionAuthority = pensionAuthority,
                FamilyRelationship = familyRelationship,
                rentalSource = rentalSource,
                CaseStatus = caseStatus.Select(x => new { Id = x.Id, Name = x.Name, NameAR = x.NameAR }).ToList(),
                Emirates = emirates,
                Areas = areas,
                ProcessTemplates = processtemplates,
                PortalCategory = portalCategory.Select(x => new { Id = x.Id, Name = x.Name, NameAR = x.NameAR }).ToList(),
                SubCategory = subCategory,
                Center = center,
                DocumentProcessTemplate = documentProcessTemplate.Select(x => new { Id = x.Id, Name = x.Name, NameAR = x.NameAR }).ToList(),
                OtherEntities = otherentities,
                ComplaintTopics = complaintTopics,
                ComplaintServices = complaintServices,
                ComplaintSubServices = complaintSubServices,
                PortalPersona = portalPersona,
                SubPersona = subPersona,
                InflationCategory = inflationCategory,
                InstallmentsRates=installmentRates,
                ReasonsToEditInflation = reasonToEditInflation,
                ReasonsToEditSwp = reasonToEditSwp,
                universities = Universities
            };

            return Ok(Responses.Success(masterData));
        }
    }
}
