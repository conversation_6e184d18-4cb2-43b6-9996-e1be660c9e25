﻿using MOCD_CommonAPI.Contact;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.OTP.Requests
{
    public class ValidateOTP
    {
        public string PhoneNumber { get; set; }
        public string EmiratesId { get; set; }
        public int Code { get; set; }
        public Guid CaseId { get; set; }
    }

    public class ValidateOTPResponse
    {
        public string EmiratesID { get; set; }
        public bool IsExistingBeneficiary { get; set; }
        public Guid ContactId { get; set; }
        public bool IsICADetailsUpdated { get; set; }
        public bool IsFamilyBookDetailsUpdated { get; set; }
        public DateTime? EmiratesIDExpiryDate { get; set; }
        public bool IsEmirates { get; set; }
        public bool EligibleHousing { get; set; }
        public bool EligibleEducation { get; set; }
        public bool EligibleInflation { get; set; }
        public int Age { get; set; }
        public Guid IdCase { get; set; }
    }

    public class ValidateGuardianOTPResponse
    {
        public ValidateOTPResponse validateOTPResponse { get; set; }
        public RetrieveContactResponse retrieveContactResponse { get; set; }
        public bool IsGuardianEligible { get; set; }
    }
}
