﻿using Microsoft.Xrm.Sdk;
using MOCD_CommonAPI.Constant;
using MOCD_CommonAPI.OTP.Requests;
using MOCD_CRMWrapperServices.Infrastructure;
using MOCD_CRMWrapperServices.Services;
using MOCD_ExternalAPI.EmiratesOTP;
using MOCD_ExternalAPI.FamilyInformationAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace MOCD_CRMWrapperServices.Controllers
{
    [RoutePrefix("API/Mohesr")]
    public class MohesrController : ApiController
    {
        private static IOrganizationService service;
        public MohesrController()
        {
            service = CRMConnection.GetCrmConnection();
        }

        [HttpGet, Route("GetMohesrStudentDetails")]
        public async Task<IHttpActionResult> GetMohesrStudentDetails(string emiratesId)
        {
            try
            {
                if (string.IsNullOrEmpty(emiratesId))
                    throw new Exception(CustomMessages.EmiratesId);
                var studentInfo = await MohesrService.GetMohesrStudentInfo(service, emiratesId);
                return Ok(Responses.Success(studentInfo));
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

    }
}