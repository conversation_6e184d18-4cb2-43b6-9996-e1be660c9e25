﻿using Newtonsoft.Json;

public class ABeneficiaryInformation
{
    [JsonProperty("@i:nil")]
    public string inil { get; set; }
}

public class AMemberInformationResponse
{
    [JsonProperty("a:Address")]
    public object aAddress { get; set; }

    [JsonProperty("a:City")]
    public object aCity { get; set; }

    [JsonProperty("a:DOB")]
    public string aDOB { get; set; }

    [JsonProperty("a:Email")]
    public object aEmail { get; set; }

    [JsonProperty("a:Emirate")]
    public object aEmirate { get; set; }

    [JsonProperty("a:FamilyBookNo")]
    public string aFamilyBookNo { get; set; }

    [JsonProperty("a:Fax")]
    public object aFax { get; set; }

    [JsonProperty("a:MemberFullNameArabic")]
    public string aMemberFullNameArabic { get; set; }

    [JsonProperty("a:MemberFullNameEnglish")]
    public string aMemberFullNameEnglish { get; set; }

    [JsonProperty("a:MemberType")]
    public string aMemberType { get; set; }

    [JsonProperty("a:Mobile")]
    public object aMobile { get; set; }

    [JsonProperty("a:NationalID")]
    public string aNationalID { get; set; }

    [JsonProperty("a:POBOX")]
    public object aPOBOX { get; set; }

    [JsonProperty("a:PensionAmount")]
    public string aPensionAmount { get; set; }

    [JsonProperty("a:PensionNumber")]
    public string aPensionNumber { get; set; }

    [JsonProperty("a:PensionStartDate")]
    public string aPensionStartDate { get; set; }

    [JsonProperty("a:Phone")]
    public APhone aPhone { get; set; }

    [JsonProperty("a:beneficiaryInformation")]
    public ABeneficiaryInformation abeneficiaryInformation { get; set; }
}

public class APhone
{
    [JsonProperty("@i:nil")]
    public string inil { get; set; }
}

public class HResponseCode
{
    [JsonProperty("@xmlns:h")]
    public string xmlnsh { get; set; }

    [JsonProperty("#text")]
    public string text { get; set; }
}

public class HResponseDescription
{
    [JsonProperty("@xmlns:h")]
    public string xmlnsh { get; set; }

    [JsonProperty("#text")]
    public string text { get; set; }
}

public class HTransactionRefNo
{
    [JsonProperty("@xmlns:h")]
    public string xmlnsh { get; set; }

    [JsonProperty("#text")]
    public string text { get; set; }
}

public class MemberInformation
{
    [JsonProperty("@xmlns")]
    public string xmlns { get; set; }

    [JsonProperty("@xmlns:a")]
    public string xmlnsa { get; set; }

    [JsonProperty("@xmlns:i")]
    public string xmlnsi { get; set; }

    [JsonProperty("a:MemberInformationResponse")]
    public AMemberInformationResponse aMemberInformationResponse { get; set; }
}

public class Root
{
    [JsonProperty("?xml")]
    public Xml xml { get; set; }

    [JsonProperty("s:Envelope")]
    public SEnvelope sEnvelope { get; set; }
}

public class SBody
{
    public MemberInformation memberInformation { get; set; }
}

public class SEnvelope
{
    [JsonProperty("@xmlns:s")]
    public string xmlnss { get; set; }

    [JsonProperty("s:Header")]
    public SHeader sHeader { get; set; }

    [JsonProperty("s:Body")]
    public SBody sBody { get; set; }
}

public class SHeader
{
    [JsonProperty("h:ResponseCode")]
    public HResponseCode hResponseCode { get; set; }

    [JsonProperty("h:ResponseDescription")]
    public HResponseDescription hResponseDescription { get; set; }

    [JsonProperty("h:TransactionRefNo")]
    public HTransactionRefNo hTransactionRefNo { get; set; }
}

public class Xml
{
    [JsonProperty("@version")]
    public string version { get; set; }

    [JsonProperty("@encoding")]
    public string encoding { get; set; }
}
