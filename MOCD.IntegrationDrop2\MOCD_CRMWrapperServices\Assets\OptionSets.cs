
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>

//------------------------------------------------------------------------------

namespace MOCD_CRMWrapperServices.Assets
{


    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_AccountCategoryCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Preferred Customer", 0)]
        PreferredCustomer = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Standard", 1)]
        Standard = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_AccountClassificationCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_AccountRatingCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address1_AddressTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Bill To", 0)]
        BillTo = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 3)]
        Other = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Primary", 2)]
        Primary = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ship To", 1)]
        ShipTo = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address1_FreightTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("FOB", 0)]
        FOB = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No Charge", 1)]
        NoCharge = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address1_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Airborne", 0)]
        Airborne = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("DHL", 1)]
        DHL = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("FedEx", 2)]
        FedEx = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Full Load", 5)]
        FullLoad = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Postal Mail", 4)]
        PostalMail = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("UPS", 3)]
        UPS = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Will Call", 6)]
        WillCall = 7,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address2_AddressTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address2_FreightTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_Address2_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_BusinessTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_CustomerSizeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_CustomerTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Competitor", 0)]
        Competitor = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Consultant", 1)]
        Consultant = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Customer", 2)]
        Customer = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Influencer", 5)]
        Influencer = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Investor", 3)]
        Investor = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 11)]
        Other = 12,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Partner", 4)]
        Partner = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Press", 6)]
        Press = 7,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Prospect", 7)]
        Prospect = 8,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Reseller", 8)]
        Reseller = 9,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Supplier", 9)]
        Supplier = 10,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Vendor", 10)]
        Vendor = 11,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_IndustryCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Accounting", 0)]
        Accounting = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Agriculture and Non-petrol Natural Resource Extraction", 1)]
        AgricultureandNonpetrolNaturalResourceExtraction = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Broadcasting Printing and Publishing", 2)]
        BroadcastingPrintingandPublishing = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Brokers", 3)]
        Brokers = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Building Supply Retail", 4)]
        BuildingSupplyRetail = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Business Services", 5)]
        BusinessServices = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Consulting", 6)]
        Consulting = 7,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Consumer Services", 7)]
        ConsumerServices = 8,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Design, Direction and Creative Management", 8)]
        DesignDirectionandCreativeManagement = 9,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Distributors, Dispatchers and Processors", 9)]
        DistributorsDispatchersandProcessors = 10,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Doctor\'s Offices and Clinics", 10)]
        DoctorsOfficesandClinics = 11,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Durable Manufacturing", 11)]
        DurableManufacturing = 12,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Eating and Drinking Places", 12)]
        EatingandDrinkingPlaces = 13,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Entertainment Retail", 13)]
        EntertainmentRetail = 14,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Equipment Rental and Leasing", 14)]
        EquipmentRentalandLeasing = 15,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Financial", 15)]
        Financial = 16,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Food and Tobacco Processing", 16)]
        FoodandTobaccoProcessing = 17,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inbound Capital Intensive Processing", 17)]
        InboundCapitalIntensiveProcessing = 18,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inbound Repair and Services", 18)]
        InboundRepairandServices = 19,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Insurance", 19)]
        Insurance = 20,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Legal Services", 20)]
        LegalServices = 21,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Non-Durable Merchandise Retail", 21)]
        NonDurableMerchandiseRetail = 22,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Outbound Consumer Service", 22)]
        OutboundConsumerService = 23,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Petrochemical Extraction and Distribution", 23)]
        PetrochemicalExtractionandDistribution = 24,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Service Retail", 24)]
        ServiceRetail = 25,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("SIG Affiliations", 25)]
        SIGAffiliations = 26,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Social Services", 26)]
        SocialServices = 27,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Special Outbound Trade Contractors", 27)]
        SpecialOutboundTradeContractors = 28,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Specialty Realty", 28)]
        SpecialtyRealty = 29,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Transportation", 29)]
        Transportation = 30,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Utility Creation and Distribution", 30)]
        UtilityCreationandDistribution = 31,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Vehicle Retail", 31)]
        VehicleRetail = 32,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Wholesale", 32)]
        Wholesale = 33,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_mocd_TypeofAccount
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom It May Concern", 0, "#0000ff")]
        ToWhomItMayConcern = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_OwnershipCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 3)]
        Other = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Private", 1)]
        Private = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Public", 0)]
        Public = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Subsidiary", 2)]
        Subsidiary = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_PaymentTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("2% 10, Net 30", 1)]
        _210Net30 = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 30", 0)]
        Net30 = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 45", 2)]
        Net45 = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 60", 3)]
        Net60 = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_PreferredAppointmentDayCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Friday", 5)]
        Friday = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Monday", 1)]
        Monday = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Saturday", 6)]
        Saturday = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sunday", 0)]
        Sunday = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Thursday", 4)]
        Thursday = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Tuesday", 2)]
        Tuesday = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Wednesday", 3)]
        Wednesday = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_PreferredAppointmentTimeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Afternoon", 1)]
        Afternoon = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Evening", 2)]
        Evening = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Morning", 0)]
        Morning = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_PreferredContactMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Any", 0)]
        Any = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Email", 1)]
        Email = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Fax", 3)]
        Fax = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mail", 4)]
        Mail = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Phone", 2)]
        Phone = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0, "#0000ff")]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft", 1, "#0000ff")]
        Draft = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Account_TerritoryCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum activitypointer_DeliveryPriorityCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("High", 2)]
        High = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Low", 0)]
        Low = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Normal", 1)]
        Normal = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_AccountRoleCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Decision Maker", 0)]
        DecisionMaker = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Employee", 1)]
        Employee = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Influencer", 2)]
        Influencer = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address1_AddressTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Bill To", 0)]
        BillTo = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 3)]
        Other = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Primary", 2)]
        Primary = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ship To", 1)]
        ShipTo = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address1_FreightTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("FOB", 0)]
        FOB = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No Charge", 1)]
        NoCharge = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address1_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Airborne", 0)]
        Airborne = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("DHL", 1)]
        DHL = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("FedEx", 2)]
        FedEx = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Full Load", 5)]
        FullLoad = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Postal Mail", 4)]
        PostalMail = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("UPS", 3)]
        UPS = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Will Call", 6)]
        WillCall = 7,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address2_AddressTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address2_FreightTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address2_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address3_AddressTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address3_FreightTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_Address3_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_CustomerSizeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_CustomerTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_EducationCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_FamilyStatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Divorced", 2)]
        Divorced = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Married", 1)]
        Married = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Single", 0)]
        Single = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Widowed", 3)]
        Widowed = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_GenderCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Female", 1)]
        Female = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Male", 0)]
        Male = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_HasChildrenCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_LeadSourceCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_msdyn_decisioninfluencetag
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Blocker", 2, "#FF0000")]
        Blocker = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Decision maker", 0, "#32C100")]
        Decisionmaker = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Influencer", 1, "#FFD74B")]
        Influencer = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Unknown", 3, "#E1DFDD")]
        Unknown = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_msdyn_orgchangestatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ignore", 2, "#0000ff")]
        Ignore = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No Feedback", 0, "#0000ff")]
        NoFeedback = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not at Company", 1, "#0000ff")]
        NotatCompany = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_PaymentTermsCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("2% 10, Net 30", 1)]
        _210Net30 = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 30", 0)]
        Net30 = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 45", 2)]
        Net45 = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Net 60", 3)]
        Net60 = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_PreferredAppointmentDayCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Friday", 5)]
        Friday = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Monday", 1)]
        Monday = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Saturday", 6)]
        Saturday = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sunday", 0)]
        Sunday = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Thursday", 4)]
        Thursday = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Tuesday", 2)]
        Tuesday = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Wednesday", 3)]
        Wednesday = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_PreferredAppointmentTimeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Afternoon", 1)]
        Afternoon = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Evening", 2)]
        Evening = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Morning", 0)]
        Morning = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_PreferredContactMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Any", 0)]
        Any = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Email", 1)]
        Email = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Fax", 3)]
        Fax = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mail", 4)]
        Mail = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Phone", 2)]
        Phone = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_ShippingMethodCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Contact_TerritoryCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Action_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_actionactiontype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Account", 1, "#0000ff")]
        Account = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contact", 2, "#0000ff")]
        Contact = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Current Step", 3, "#0000ff")]
        CurrentStep = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Custom Code", 9, "#0000ff")]
        CustomCode = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Initiate Sub-Request", 5, "#0000ff")]
        InitiateSubRequest = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Linked Request", 6, "#0000ff")]
        LinkedRequest = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Loop Task", 4, "#0000ff")]
        LoopTask = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Request", 0, "#0000ff")]
        Request = 110000008,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Send Email", 7, "#0000ff")]
        SendEmail = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Send SMS", 8, "#0000ff")]
        SendSMS = 110000004,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_AssignTo
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Team", 1, "#0000ff")]
        Team = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("User", 0, "#0000ff")]
        User = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_BusinessRule_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Condition_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ConditionOperator
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contains", 6, "#0000ff")]
        Contains = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contains Data", 10, "#0000ff")]
        ContainsData = 110000010,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contain Values", 8, "#0000ff")]
        ContainValues = 110000008,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Does Not Contain", 7, "#0000ff")]
        DoesNotContain = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Does Not Contain Data", 11, "#0000ff")]
        DoesNotContainData = 110000011,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Does Not Contain Values", 9, "#0000ff")]
        DoesNotContainValues = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Equals", 0, "#0000ff")]
        Equals = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Greater Than", 2, "#0000ff")]
        GreaterThan = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Greater Than Equal To", 4, "#0000ff")]
        GreaterThanEqualTo = 110000004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Less Than", 3, "#0000ff")]
        LessThan = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Less Than Equal To", 5, "#0000ff")]
        LessThanEqualTo = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not Equals", 1, "#0000ff")]
        NotEquals = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_customcodeconfiguration_hexa_Type
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Action", 0, "#0000ff")]
        Action = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Condition", 1, "#0000ff")]
        Condition = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Email", 2, "#0000ff")]
        Email = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sms", 3, "#0000ff")]
        Sms = 110000002,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_customcodeconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_customcomponentconfiguration_hexa_ConfigurationFor
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Demo", 1, "#0000ff")]
        Demo = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Document Viewer", 0, "#0000ff")]
        DocumentViewer = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_customcomponentconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_DocumentTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_environmentconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_hexaConfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_pricelist_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_pricelistitem_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_processbuilder_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ProcessDocumentTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ProcessPriceItemTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ProcessStatusTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_processstatustemplatetype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0, "#0000ff")]
        Active = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved", 1, "#0000ff")]
        Approved = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Cancelled", 2, "#0000ff")]
        Cancelled = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Rejected", 3, "#0000ff")]
        Rejected = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ProcessStepTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_processsteptemplatesteptype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Document Check", 0, "#0000ff")]
        DocumentCheck = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Quick Action", 1, "#0000ff")]
        QuickAction = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_ProcessTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_product_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_hexa_RequestType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Linked Request", 0, "#0000ff")]
        LinkedRequest = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sub Request", 1, "#0000ff")]
        SubRequest = *********,
    }



    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_DocumentTemplate
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Exception", 0, "#0000ff")]
        Exception = 662410006,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - Female with Family", 4, "#0000ff")]
        ICPorOtherFemalewithFamily = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - Individual Female", 2, "#0000ff")]
        ICPorOtherIndividualFemale = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - Individual Male", 1, "#0000ff")]
        ICPorOtherIndividualMale = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - Male with Family", 3, "#0000ff")]
        ICPorOtherMalewithFamily = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - No Allowance Female", 6, "#0000ff")]
        ICPorOtherNoAllowanceFemale = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ICP or Other - No Allowance Male", 5, "#0000ff")]
        ICPorOtherNoAllowanceMale = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom It May Concern - Individual Female", 9, "#0000ff")]
        ToWhomItMayConcernIndividualFemale = 662410011,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom It May Concern - Individual Male", 10, "#0000ff")]
        ToWhomItMayConcernIndividualMale = 662410009,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom It May Concern - No Allowance Female", 7, "#0000ff")]
        ToWhomItMayConcernNoAllowanceFemale = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom It May Concern - No Allowance Male", 8, "#0000ff")]
        ToWhomItMayConcernNoAllowanceMale = 662410008,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_entityaddressed
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 1, "#0000ff")]
        Other = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("To Whom it May Concern", 0, "#0000ff")]
        ToWhomitMayConcern = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_entityname
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Community Development Authority – Dubai", 2, "#0000ff")]
        CommunityDevelopmentAuthorityDubai = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Department of Community development - Abu Dhabi", 1, "#0000ff")]
        DepartmentofCommunitydevelopmentAbuDhabi = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation", 4, "#0000ff")]
        Inflation = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ministry of Community Development", 0, "#0000ff")]
        MinistryofCommunityDevelopment = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Social Services Department - Sharjah", 3, "#0000ff")]
        SocialServicesDepartmentSharjah = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_entityreceivedfrom
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Community Development Authority – Dubai", 2, "#0000ff")]
        CommunityDevelopmentAuthorityDubai = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Department of Community development - Abu Dhabi", 1, "#0000ff")]
        DepartmentofCommunitydevelopmentAbuDhabi = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ministry of Community Development", 0, "#0000ff")]
        MinistryofCommunityDevelopment = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Social Services Department - Sharjah", 3, "#0000ff")]
        SocialServicesDepartmentSharjah = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_InflationType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Base Allowance + Inflation", 1, "#0000ff")]
        BaseAllowanceInflation = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Nominated by Social Support Entity", 0, "#0000ff")]
        NominatedbySocialSupportEntity = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Stand Alone", 2, "#0000ff")]
        StandAlone = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_SMSCategory
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Group 1", 0, "#0000ff")]
        Group1 = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Group 2", 1, "#0000ff")]
        Group2 = 100000001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Group 3", 2, "#0000ff")]
        Group3 = 100000002,
        [System.Runtime.Serialization.EnumMemberAttribute()]

        [OptionSetMetadataAttribute("Group 4", 2, "#0000ff")]
        Group4 = 100000003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Group 5", 2, "#0000ff")]
        Group5 = 100000004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Group 6", 2, "#0000ff")]
        Group6 = 100000005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Group 7", 2, "#0000ff")]
        Group7 = 100000006,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Group 8", 2, "#0000ff")]
        Group8 = 100000007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Group 9", 2, "#0000ff")]
        Group9 = 100000008,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Group 9", 2, "#0000ff")]
        Group10 = 100000009,
        //
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_validateutilityapistatus
    {
        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No", 1, "#0000ff")]
        Success = 1,
        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Yes", 0, "#0000ff")]
        Failed = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_isutilityaccountholderafirstdegreeme
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No", 1, "#0000ff")]
        No = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Yes", 0, "#0000ff")]
        Yes = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_LegacyCaseType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type I", 0, "#0000ff")]
        TypeI = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type II", 1, "#0000ff")]
        TypeII = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type III", 2, "#0000ff")]
        TypeIII = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_legacytype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type 1", 0, "#0000ff")]
        Type1 = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type 2", 1, "#0000ff")]
        Type2 = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type 3", 2, "#0000ff")]
        Type3 = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_mocd_lettertype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Family Member", 1, "#0000ff")]
        FamilyMember = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Own", 0, "#0000ff")]
        Own = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_Request_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0, "#0000ff")]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved", 3, "#0000ff")]
        Approved = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Cancelled", 2, "#0000ff")]
        Cancelled = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Rejected", 1, "#0000ff")]
        Rejected = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_RequestDocument_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0, "#0000ff")]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved", 6, "#0000ff")]
        Approved = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Expired", 8, "#0000ff")]
        Expired = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Generated", 7, "#0000ff")]
        Generated = 110000004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 5, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Upload", 2, "#0000ff")]
        PendingUpload = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Re-Upload", 1, "#0000ff")]
        ReUpload = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Uploaded", 3, "#0000ff")]
        Uploaded = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Uploaded On Portal", 4, "#0000ff")]
        UploadedOnPortal = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_RequestPriceItem_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Added", 0, "#0000ff")]
        Added = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Booked", 1, "#0000ff")]
        Booked = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Consumed", 3, "#0000ff")]
        Consumed = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 4, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Invoiced", 2, "#0000ff")]
        Invoiced = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_RequestStep_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_StepStatusTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_stepstatustemplatetype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("End", 2, "#0000ff")]
        End = 110000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Intermediate", 1, "#0000ff")]
        Intermediate = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Start", 0, "#0000ff")]
        Start = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_StepTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_StepTransitionTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum hexa_TransitionTemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_CaseOriginCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Customer Pulse", 23, "#0000ff")]
        CustomerPulse = 662410019,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Diwan Shaikh", 18, "#0000ff")]
        DiwanShaikh = 662410014,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Email", 25, "#00B294")]
        Email = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Facebook", 27, "#0086FF")]
        Facebook = 2483,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Face to Face", 1, "#0000ff")]
        FacetoFace = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("FNC - Minister\'s Office", 19, "#0000ff")]
        FNCMinistersOffice = 662410015,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Her excellency the Minister", 21, "#0000ff")]
        HerexcellencytheMinister = 662410017,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Her excellency the Under Secretary", 22, "#0000ff")]
        HerexcellencytheUnderSecretary = 662410018,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Internal Audit", 17, "#0000ff")]
        InternalAudit = 662410013,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Live Chat on Website", 6, "#0000ff")]
        LiveChatonWebsite = 662410006,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mobile version of the Website - Chatbot", 8, "#0000ff")]
        MobileversionoftheWebsiteChatbot = 662410008,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("MOCD App", 7, "#0000ff")]
        MOCDApp = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mystery Shopper", 16, "#0000ff")]
        MysteryShopper = 662410012,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("NABD", 20, "#0000ff")]
        NABD = 662410016,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Phone", 24, "#FCD116")]
        Phone = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Radio Noor - Dubai", 13, "#0000ff")]
        RadioNoorDubai = 662410020,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Radio Station - Abu Dhabi", 12, "#0000ff")]
        RadioStationAbuDhabi = 662410011,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Radio Station - Ajman", 15, "#0000ff")]
        RadioStationAjman = 662410022,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Radio Station - Sharjah", 14, "#0000ff")]
        RadioStationSharjah = 662410021,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Social Media - Instagram", 10, "#0000ff")]
        SocialMediaInstagram = 662410010,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Social Media - Twitter", 11, "#0000ff")]
        SocialMediaTwitter = 662410023,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ticketing Reach", 0, "#0000ff")]
        TicketingReach = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Twitter", 28, "#0086FF")]
        Twitter = 3986,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Web", 26, "#FF8C00")]
        Web = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Website - Communicate with the Leadership", 4, "#0000ff")]
        WebsiteCommunicatewiththeLeadership = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Website - Inquiry", 5, "#0000ff")]
        WebsiteInquiry = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Website - Quality Email", 3, "#0000ff")]
        WebsiteQualityEmail = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Website - Suggestions and Complaints", 2, "#0000ff")]
        WebsiteSuggestionsandComplaints = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("WhatsApp", 9, "#0000ff")]
        WhatsApp = 662410009,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_CaseTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Complaint", 0, "#FF8C00")]
        Complaint = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inquiry", 1, "#E71022")]
        Inquiry = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Suggestion", 2, "#0000ff")]
        Suggestion = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Thank You", 3, "#0072C6")]
        ThankYou = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_ContractServiceLevelCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Bronze", 2)]
        Bronze = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Gold", 0)]
        Gold = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Silver", 1)]
        Silver = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_CustomerSatisfactionCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Dissatisfied", 3)]
        Dissatisfied = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Neutral", 2)]
        Neutral = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Satisfied", 1)]
        Satisfied = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Very Dissatisfied", 4)]
        VeryDissatisfied = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Very Satisfied", 0)]
        VerySatisfied = 5,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_FirstResponseSLAStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 0)]
        InProgress = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Nearing Noncompliance", 1)]
        NearingNoncompliance = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Noncompliant", 3)]
        Noncompliant = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Succeeded", 2)]
        Succeeded = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_IncidentStageCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_mocd_AppealStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Is Appealed", 0, "#0000ff")]
        IsAppealed = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No Appealed", 1, "#0000ff")]
        NoAppealed = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_mocd_AssignedTo
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("L1", 0, "#0000ff")]
        L1 = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("L2", 1, "#0000ff")]
        L2 = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("L3", 2, "#0000ff")]
        L3 = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_mocd_assigneelevel
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("L1", 0, "#0000ff")]
        L1 = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("L2", 1, "#0000ff")]
        L2 = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("L3", 2, "#0000ff")]
        L3 = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_mocd_InternalStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Applicable", 0, "#0000ff")]
        Applicable = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Closed", 9, "#0000ff")]
        Closed = 662410009,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Done", 3, "#0000ff")]
        Done = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Duplicate", 2, "#0000ff")]
        Duplicate = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Escalated to Management", 6, "#0000ff")]
        EscalatedtoManagement = 662410006,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Escalated to Under Secretary", 7, "#0000ff")]
        EscalatedtoUnderSecretary = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Information Provided", 5, "#0000ff")]
        InformationProvided = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not Applicable", 1, "#0000ff")]
        NotApplicable = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Resolved", 8, "#0000ff")]
        Resolved = 662410008,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Submitted", 4, "#0000ff")]
        Submitted = 662410004,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_PriorityCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("P1", 0)]
        P1 = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("P2", 1)]
        P2 = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("P3", 2)]
        P3 = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("P4", 3, "#0000ff")]
        P4 = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_ResolveBySLAStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 0)]
        InProgress = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Nearing Noncompliance", 1)]
        NearingNoncompliance = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Noncompliant", 3)]
        Noncompliant = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Succeeded", 2)]
        Succeeded = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_SeverityCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default Value", 0)]
        DefaultValue = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum Incident_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Assigned to L1", 2, "#F9DCD1")]
        AssignedtoL1 = 3,
        AssignedtoL2 = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Assigned to L3", 5, "#0000ff")]
        AssignedtoL3 = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Cancelled", 12, "#EDEBE9")]
        Cancelled = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Closed", 10, "#0000ff")]
        Closed = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Done", 11, "#0000ff")]
        Done = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft", 0, "#E7EFFF")]
        Draft = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Information Provided", 9, "#CFE4FA")]
        InformationProvided = 1000,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not Applicable", 7, "#0000ff")]
        NotApplicable = 662410010,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("On Hold", 1, "#EEDBEB")]
        OnHold = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Rejected", 13, "#806c00")]
        Rejected = 2000,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Reopened", 5, "#0000ff")]
        Reopened = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Resolved", 8, "#CAEAD8")]
        Resolved = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Submitted", 2, "#F9DCD1")]
        Submitted = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Under Consideration", 3, "#0000ff")]
        UnderConsideration = 662410009,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_accomodationtype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_address_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancebudget_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancecategory_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancegroup_mocd_ConsiderIncome
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("False", 1, "#0000ff")]
        False = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("True", 0, "#0000ff")]
        True = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancegroup_mocd_Entity
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case(Hexa_Request)", 0, "#0000ff")]
        Case_Hexa_Request = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contact", 1, "#0000ff")]
        Contact = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancegroup_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancepayout_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Disbursed", 3, "#0000ff")]
        Disbursed = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft", 0, "#0000ff")]
        Draft = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft - Refund", 6, "#0000ff")]
        DraftRefund = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 9, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("On Hold", 5, "#0000ff")]
        OnHold = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Approvals", 4, "#0000ff")]
        PendingApprovals = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Processed", 2, "#0000ff")]
        Processed = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Processed - Refund", 8, "#0000ff")]
        ProcessedRefund = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ready for Processing", 1, "#0000ff")]
        ReadyforProcessing = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ready for Processing - Refund", 7, "#0000ff")]
        ReadyforProcessingRefund = 662410006,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancepayoutdetail_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft", 0, "#0000ff")]
        Draft = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft - Refund", 5, "#0000ff")]
        DraftRefund = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 8, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("On Hold", 4, "#0000ff")]
        OnHold = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Approvals", 3, "#0000ff")]
        PendingApprovals = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Processed", 2, "#0000ff")]
        Processed = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Processed - Refund", 7, "#0000ff")]
        ProcessedRefund = 662410006,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ready for Processing", 1, "#0000ff")]
        ReadyforProcessing = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ready for Processing - Refund", 6, "#0000ff")]
        ReadyforProcessingRefund = 662410005,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancesubcategory_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancetransaction_mocd_NewAllowanceStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Amount Decreased but Maintained", 1, "#0000ff")]
        AmountDecreasedbutMaintained = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Amount Increased", 0, "#0000ff")]
        AmountIncreased = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ineligible & Amount Maintained", 2, "#0000ff")]
        IneligibleAmountMaintained = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Same Amount", 3, "#0000ff")]
        SameAmount = 662410003,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancetransaction_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved", 3, "#0000ff")]
        Approved = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Disbursed", 4, "#0000ff")]
        Disbursed = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Disburse Old Amount", 7, "#0000ff")]
        DisburseOldAmount = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft", 0, "#0000ff")]
        Draft = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft - Refund", 9, "#0000ff")]
        DraftRefund = 662410010,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 11, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("On Hold", 5, "#0000ff")]
        OnHold = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending for processing", 1, "#0000ff")]
        Pendingforprocessing = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ready for review", 2, "#0000ff")]
        Readyforreview = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Stopped", 8, "#0000ff")]
        Stopped_Active = 662410009,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Stopped", 10, "#0000ff")]
        Stopped_Inactive = 662410008,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Waitlisted", 6, "#0000ff")]
        Waitlisted = 662410006,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancetransactiondetails_mocd_IsPOD
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No", 0, "#0000ff")]
        No = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Yes", 1, "#0000ff")]
        Yes = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancetransactiondetails_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_apiconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_apilog_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_appeal_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Accepted", 3, "#0000ff")]
        Accepted = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Rejected", 2, "#0000ff")]
        Rejected = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Submitted", 0, "#0000ff")]
        Submitted = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Under Review", 1, "#0000ff")]
        UnderReview = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_area_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_attachment_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_AttachmentType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Charity Expense - Entities", 7, "#0000ff")]
        CharityExpenseEntities = 8,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Charity Expense - Individuals", 8, "#0000ff")]
        CharityExpenseIndividuals = 9,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Employees", 1, "#0000ff")]
        Employees = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Fundraising Permissions", 3, "#0000ff")]
        FundraisingPermissions = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Fundraising Projects", 4, "#0000ff")]
        FundraisingProjects = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Fundraising Resources - Entities", 5, "#0000ff")]
        FundraisingResourcesEntities = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Fundraising Resources - Individuals", 6, "#0000ff")]
        FundraisingResourcesIndividuals = 7,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Members", 0, "#0000ff")]
        Members = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Support and Assistance", 2, "#0000ff")]
        SupportandAssistance = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_bank_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_beneficiaryallowancegroup_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_beneficiaryhistory_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_BeneficiaryIncome_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Verification", 1, "#0000ff")]
        PendingVerification = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Verified", 0, "#0000ff")]
        Verified = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_beneficiarypension_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Verification", 1, "#0000ff")]
        PendingVerification = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Verified", 0, "#0000ff")]
        Verified = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_BeneficiaryRentalIncome_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Verification", 1, "#0000ff")]
        PendingVerification = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Verified", 0, "#0000ff")]
        Verified = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_beneficiarytradelicense_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Verification", 1, "#0000ff")]
        PendingVerification = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Verified", 0, "#0000ff")]
        Verified = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_BeneficiaryType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Beneficiary", 0, "#0000ff")]
        Beneficiary = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Family", 1, "#0000ff")]
        Family = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_BuildingPropertyType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Grant", 2, "#0000ff")]
        Grant = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ownership", 1, "#0000ff")]
        Ownership = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Rent", 0, "#0000ff")]
        Rent = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Usufruct", 3, "#0000ff")]
        Usufruct = 662410003,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casebeneficiaryeducationalallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casebeneficiaryincome_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Verification", 1, "#0000ff")]
        PendingVerification = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Verified", 0, "#0000ff")]
        Verified = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casebeneficiarypension_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Verification", 1, "#0000ff")]
        PendingVerification = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Verified", 0, "#0000ff")]
        Verified = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casebeneficiaryrentalincome_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Verification", 1, "#0000ff")]
        PendingVerification = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Verified", 0, "#0000ff")]
        Verified = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casebeneficiarytradelicense_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Verification", 1, "#0000ff")]
        PendingVerification = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Verified", 0, "#0000ff")]
        Verified = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casefamilybook_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casehistory_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_caseresolutiontemplate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_casetype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_CaseType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Documents Expiry", 5, "#0000ff")]
        DocumentsExpiry = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Edit Case", 2, "#0000ff")]
        EditCase = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Exiting Case", 1, "#0000ff")]
        ExitingCase = 6,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation Case", 7, "#0000ff")]
        InflationCase = 8,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Monthly Review", 3, "#0000ff")]
        MonthlyReview = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("New Case", 0, "#0000ff")]
        NewCase = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Periodic Review", 4, "#0000ff")]
        PeriodicReview = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Top Up Allowance", 6, "#0000ff")]
        TopUpAllowance = 7,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_category_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_center_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_childallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_city_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_country_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_crmconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_DataSource
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation Data", 2, "#0000ff")]
        InflationData = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Legacy Data", 1, "#0000ff")]
        LegacyData = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Legacy Data-New Rule", 0, "#0000ff")]
        LegacyDataNewRule = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Local Entity Data", 3, "#0000ff")]
        LocalEntityData = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 4, "#0000ff")]
        Other = 662410003,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_disabilitycardcommunicationtype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_disabilitycardprinter_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_disabilitydiagnosesauthority_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_disabilityequipment_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_disabilityquaternarytype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_disabilitysubtype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_disabilitytertiarytype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_disabilitytype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_document_mocd_direction
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Incoming", 0, "#0000ff")]
        Incoming = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Outgoing", 1, "#0000ff")]
        Outgoing = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_document_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_documentconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_documenttype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_education_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_educationallowance_mocd_Entity
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case", 0, "#0000ff")]
        Case = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Family Members", 1, "#0000ff")]
        FamilyMembers = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_educationallowance_mocd_secondentity
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case", 0, "#0000ff")]
        Case = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contact", 1, "#0000ff")]
        Contact = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_educationallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_educationcategorycurriculum
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("IB", 3, "#0000ff")]
        IB = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 4, "#0000ff")]
        Other = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Public Education", 0, "#0000ff")]
        PublicEducation = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("UK", 2, "#0000ff")]
        UK = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("US", 1, "#0000ff")]
        US = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_EligibilityCheckSource
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Community Development Authority – Dubai", 2, "#0000ff")]
        CommunityDevelopmentAuthorityDubai = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Department of Community development - Abu Dhabi", 1, "#0000ff")]
        DepartmentofCommunitydevelopmentAbuDhabi = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation", 4, "#0000ff")]
        Inflation = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ministry of Community Development", 0, "#0000ff")]
        MinistryofCommunityDevelopment = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Social Services Department - Sharjah", 3, "#0000ff")]
        SocialServicesDepartmentSharjah = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_emirate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_emirates_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_EmploymentStatus_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_EmploymentType_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_familybook_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_familyrelationship_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_FATFCategory
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Applicable", 0, "#0000ff")]
        Applicable = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not Applicable", 1, "#0000ff")]
        NotApplicable = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_FinancialTransactionStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Decrease Allowance", 1, "#0000ff")]
        DecreaseAllowance = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Increase Allowance", 0, "#0000ff")]
        IncreaseAllowance = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Maintain Allowance", 3, "#0000ff")]
        MaintainAllowance = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("New Case", 4, "#0000ff")]
        NewCase = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Stop Allowance", 2, "#0000ff")]
        StopAllowance = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_fuelpriceconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_GCSEScore
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("A*", 0, "#0000ff")]
        A_********* = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("A", 1, "#0000ff")]
        A_********* = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("B", 2, "#0000ff")]
        B = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("C", 3, "#0000ff")]
        C = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("D", 4, "#0000ff")]
        D = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("E", 5, "#0000ff")]
        E = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("F", 6, "#0000ff")]
        F = 662410006,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("G", 7, "#0000ff")]
        G = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("U", 8, "#0000ff")]
        U = 662410008,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_gender_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_housecondition_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_houseownership_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_housingallowance_mocd_firstentity
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case", 0, "#0000ff")]
        Case = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Family Members", 1, "#0000ff")]
        FamilyMembers = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_housingallowance_mocd_secondentity
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case", 0, "#0000ff")]
        Case = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contact", 1, "#0000ff")]
        Contact = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_housingallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_incomesource_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_incometype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_individualallowance_mocd_IsDisabled
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("1", 2)]
        _1 = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("False", 1, "#0000ff")]
        False = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("NULL", 3)]
        NULL = 100000001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("True", 0, "#0000ff")]
        True = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_individualallowance_mocd_IsPOD
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("1", 2)]
        _1 = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("False", 1, "#0000ff")]
        False = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("NULL", 3)]
        NULL = 100000001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("True", 0, "#0000ff")]
        True = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_individualallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_inflationcategoriesmapping_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_InflationEligibilityCategory
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Base Allowance", 0, "#0000ff")]
        BaseAllowance = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ineligible", 4, "#0000ff")]
        Ineligible = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation - 1 Member", 1, "#0000ff")]
        Inflation1Member = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation - 2 or more Members", 2, "#0000ff")]
        Inflation2ormoreMembers = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation - Local Entity", 3, "#0000ff")]
        InflationLocalEntity = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_InflationFuelAllowanceType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type 1", 0, "#0000ff")]
        Type1 = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type 2", 1, "#0000ff")]
        Type2 = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_InflationStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved By MoCD", 0, "#0000ff")]
        ApprovedByMoCD = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved by Providers", 1, "#0000ff")]
        ApprovedbyProviders = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Stopped by Local Authority", 2, "#0000ff")]
        StoppedbyLocalAuthority = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_InflationType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Base Allowance + Inflation Allowance", 1, "#0000ff")]
        BaseAllowanceInflationAllowance = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ineligible", 3, "#0000ff")]
        Ineligible = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Nominated by Local Entity", 0, "#0000ff")]
        NominatedbyLocalEntity = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Stand Alone Inflation Allowance", 2, "#0000ff")]
        StandAloneInflationAllowance = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_installmentrate_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_institutetype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_IntegrationStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Completed", 3, "#0000ff")]
        Completed = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Failed", 0, "#0000ff")]
        Failed = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 2, "#0000ff")]
        InProgress = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending", 1, "#0000ff")]
        Pending = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_intermediateexternalapiresponse_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_jobtitle_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_LastEmployment
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("I did not work before", 2, "#0000ff")]
        Ididnotworkbefore = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("I have been terminated from private sector", 1, "#0000ff")]
        Ihavebeenterminatedfromprivatesector = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("I have been terminated from Public/Semi-Government", 0, "#0000ff")]
        IhavebeenterminatedfromPublicSemiGovernment = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_LegacyCaseType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type I", 0, "#0000ff")]
        TypeI = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type II", 1, "#0000ff")]
        TypeII = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Type III", 2, "#0000ff")]
        TypeIII = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_LegacyTopUpCategory
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Category A", 0, "#0000ff")]
        CategoryA = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Category B", 1, "#0000ff")]
        CategoryB = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_letterservicefamilybook_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_licensingauthority_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_livechat_InstanceTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not Recurring", 0)]
        NotRecurring = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Exception", 3)]
        RecurringException = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Future Exception", 4)]
        RecurringFutureException = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Instance", 2)]
        RecurringInstance = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Master", 1)]
        RecurringMaster = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_livechat_PriorityCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("High", 2)]
        High = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Low", 0)]
        Low = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Normal", 1)]
        Normal = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_livechat_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Canceled", 2)]
        Canceled = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Completed", 1)]
        Completed = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Open", 0)]
        Open = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Scheduled", 3)]
        Scheduled = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_LocalHousingSupportEntities
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Abu Dhabi Housing Authority", 0, "#0000ff")]
        AbuDhabiHousingAuthority = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Directorate of Housing of Sharjah", 3, "#0000ff")]
        DirectorateofHousingofSharjah = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Mohammed Bin Rashid Housing Establishment", 1, "#0000ff")]
        MohammedBinRashidHousingEstablishment = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Sheikh Zayed Housing Program", 2, "#0000ff")]
        SheikhZayedHousingProgram = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_logdetail_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Failed", 2, "#0000ff")]
        Failed = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 3, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 0, "#0000ff")]
        InProgress = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Successful", 1, "#0000ff")]
        Successful = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_logheader_mocd_TriggerSource
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case Submission", 0, "#0000ff")]
        CaseSubmission = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Farmers Bi Monthly", 1, "#0000ff")]
        FarmersBiMonthly = 662410006,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inbound API Call", 2, "#0000ff")]
        InboundAPICall = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation Bi Monthly", 3, "#0000ff")]
        InflationBiMonthly = 662410010,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation Case Submission", 4, "#0000ff")]
        InflationCaseSubmission = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation On Demand", 5, "#0000ff")]
        InflationOnDemand = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inflation Periodic Review", 6, "#0000ff")]
        InflationPeriodicReview = 662410008,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Manually Added Family Member", 7, "#0000ff")]
        ManuallyAddedFamilyMember = 662410009,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("On Demand", 8, "#0000ff")]
        OnDemand = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Periodic Review", 9, "#0000ff")]
        PeriodicReview = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Portal", 10, "#0000ff")]
        Portal = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Refund Cases", 11, "#0000ff")]
        RefundCases = 662410011,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_logheader_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Failed", 2, "#0000ff")]
        Failed = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 3, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 0, "#0000ff")]
        InProgress = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Successful", 1, "#0000ff")]
        Successful = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_LogMessageType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Exception", 3, "#0000ff")]
        Exception = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Success", 0, "#0000ff")]
        Success = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Success with exception", 1, "#0000ff")]
        Successwithexception = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Warning", 2, "#0000ff")]
        Warning = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_maritalstatus_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_MilitaryServiceStatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Completed Military service", 1, "#0000ff")]
        CompletedMilitaryservice = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Enrolled in Military service", 0, "#0000ff")]
        EnrolledinMilitaryservice = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Exempted from Military service", 2, "#0000ff")]
        ExemptedfromMilitaryservice = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not applicable for women", 3, "#0000ff")]
        Notapplicableforwomen = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_nationality_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_NPOCategoryFundraising
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Authorized", 1, "#0000ff")]
        Authorized = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Charitable", 0, "#0000ff")]
        Charitable = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Licensed", 2, "#0000ff")]
        Licensed = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_occasionalallowance_mocd_Entity
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case", 0, "#0000ff")]
        Case = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Family Members", 1, "#0000ff")]
        FamilyMembers = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_occasionalallowance_mocd_occasionalapproach
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Automated", 0, "#0000ff")]
        Automated = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Manual", 1, "#0000ff")]
        Manual = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_occasionalallowance_mocd_secondentity
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case", 0, "#0000ff")]
        Case = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contact", 1, "#0000ff")]
        Contact = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_occasionalallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 1, "#0000ff")]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Disbursed", 0, "#0000ff")]
        Disbursed = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_occupation_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_passport_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_passporttype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_PaymentOptions
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("100%", 2, "#0000ff")]
        _100 = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("25%", 0, "#0000ff")]
        _25 = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("50%", 1, "#0000ff")]
        _50 = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_pensionauthority_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_pensiontype_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_personaccommodated_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_personadetails_mocd_entity
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case(Hexa_Request)", 1, "#0000ff")]
        Case_Hexa_Request = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contact", 0, "#0000ff")]
        Contact = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_personadetails_mocd_isnewcase
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approve", 0, "#0000ff")]
        Approve = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("N/A", 2, "#0000ff")]
        NA = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Reject", 1, "#0000ff")]
        Reject = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_personadetails_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_PersonaEngine_CaseBehaviour
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Continue", 2, "#0000ff")]
        Continue = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Detach", 1, "#0000ff")]
        Detach = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("N/A", 4, "#0000ff")]
        NA = 5,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Stop", 0, "#0000ff")]
        Stop = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Update", 3, "#0000ff")]
        Update = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_poddisabledcard_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_poddisabledcardrequest_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_podrequestsupportingequipment_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_podservicerequest_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved", 5, "#0000ff")]
        Approved = 100000003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Draft", 0, "#0000ff")]
        Draft = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 4, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 2, "#0000ff")]
        InProgress = 100000001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Rejected", 6, "#0000ff")]
        Rejected = 100000004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Returned", 3, "#0000ff")]
        Returned = 100000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Submitted", 1, "#0000ff")]
        Submitted = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_portalcategory_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_portalnotifications_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 2, "#0000ff")]
        Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Read", 1, "#0000ff")]
        Read = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Unread", 0, "#0000ff")]
        Unread = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_portalpersona_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_ProcessRunningFrequency
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Daily", 1, "#0000ff")]
        Daily = 100000001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Hourly", 0, "#0000ff")]
        Hourly = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Monthly", 3, "#0000ff")]
        Monthly = 100000003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Weekly", 2, "#0000ff")]
        Weekly = 100000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Yearly", 4, "#0000ff")]
        Yearly = 100000004,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_processstage_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_processstageaction_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0, "#0000ff")]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Completed", 1, "#0000ff")]
        Completed_Active = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Completed", 2, "#0000ff")]
        Completed_Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_processstageactionconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_processstageactiondecision_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_processstageactiondecisionconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_processstageconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_ProcessStageDecisionType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Backward", 1, "#0000ff")]
        Backward = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("CustomerReply", 3, "#0000ff")]
        CustomerReply = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Forward", 0, "#0000ff")]
        Forward = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Reject", 2, "#0000ff")]
        Reject = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_PublicEducationStream
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Advanced stream", 1, "#0000ff")]
        Advancedstream = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Elite stream", 2, "#0000ff")]
        Elitestream = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("General stream", 0, "#0000ff")]
        Generalstream = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_recurringprocess_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_refundallowancegroup_mocd_entity
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Allowance Payouts (mocd_allowancepayout)", 2, "#0000ff")]
        AllowancePayouts_mocd_allowancepayout = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Case(Hexa_Request)", 0, "#0000ff")]
        Case_Hexa_Request = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Contact", 1, "#0000ff")]
        Contact = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_refundallowancegroup_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_refunddetails_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_refundpayment_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Overdue Payment", 5, "#0000ff")]
        OverduePayment = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Payment Accumulated", 6, "#0000ff")]
        PaymentAccumulated = 662410005,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Payment Completed", 3, "#0000ff")]
        PaymentCompleted_Active = 662410007,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Payment Completed", 4, "#0000ff")]
        PaymentCompleted_Inactive = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Confirmation", 0, "#0000ff")]
        PendingConfirmation = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Pending Payment", 1, "#0000ff")]
        PendingPayment = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Stopped", 2, "#0000ff")]
        Stopped = 662410006,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_refundpaymentheader_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 0, "#0000ff")]
        InProgress = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Internal Handling", 4, "#0000ff")]
        InternalHandling = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Payment Accumulated", 3, "#0000ff")]
        PaymentAccumulated = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Refunded", 1, "#0000ff")]
        Refunded_Active = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Refunded", 2, "#0000ff")]
        Refunded_Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_refundreason_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_RejectionFor
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Base Allowance", 0, "#0000ff")]
        BaseAllowance = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Farmer Service", 1, "#0000ff")]
        FarmerService = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_rejectreason_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_religion_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_rentalsource_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_request_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_requestallowancegroup_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_RequestsStatusReason
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Approved", 3, "#0000ff")]
        Approved = 100000003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("In Progress", 1, "#0000ff")]
        InProgress = 100000001,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Rejected", 4, "#0000ff")]
        Rejected = 100000004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Returned", 2, "#0000ff")]
        Returned = 100000002,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Submitted", 0, "#0000ff")]
        Submitted = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_ScoresTypeDocument
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Advanced Placement", 1, "#0000ff")]
        AdvancedPlacement = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("EmSAT", 0, "#0000ff")]
        EmSAT = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_SemesterofCGPA
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("First term (August - December)", 0, "#0000ff")]
        Firstterm_AugustDecember = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Second term (January - May)", 1, "#0000ff")]
        Secondterm_JanuaryMay = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_service_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_servicecatalogue_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_ServiceType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Lost POD Card", 2, "#0000ff")]
        LostPODCard = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("New POD Card", 1, "#0000ff")]
        NewPODCard = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("NPO Declaration", 0, "#0000ff")]
        NPODeclaration = 111,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Renew POD Card", 3, "#0000ff")]
        RenewPODCard = 3,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_siblingallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_slaconfigurations_mocd_duedatecalculationtype
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Cut Off Day In The Month", 0, "#0000ff")]
        CutOffDayInTheMonth = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Duration", 1, "#0000ff")]
        Duration = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_slaconfigurations_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_slaoutputs_mocd_slastatus
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Expired", 0, "#0000ff")]
        Expired = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Failed", 2, "#0000ff")]
        Failed = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Succeeded", 1, "#0000ff")]
        Succeeded = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_slaoutputs_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_sms_InstanceTypeCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not Recurring", 0)]
        NotRecurring = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Exception", 3)]
        RecurringException = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Future Exception", 4)]
        RecurringFutureException = 4,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Instance", 2)]
        RecurringInstance = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Recurring Master", 1)]
        RecurringMaster = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_sms_PriorityCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("High", 2)]
        High = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Low", 0)]
        Low = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Normal", 1)]
        Normal = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_sms_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Canceled", 2)]
        Canceled = 3,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Completed", 1)]
        Completed = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Open", 0)]
        Open = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Scheduled", 3)]
        Scheduled = 4,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_spouseallowance_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_street_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_subcategory_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_subpersona_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_subservice_mocd_ApplicableFor
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Both", 2, "#0000ff")]
        Both = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Complaint", 1, "#0000ff")]
        Complaint = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inquiry", 0, "#0000ff")]
        Inquiry = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_subservice_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_thresholdconfiguration_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_topic_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_tradesource_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_tribe_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_university_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_universityacademicyear_StatusCode
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Active", 0)]
        Active = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Inactive", 1)]
        Inactive = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_UniversityCategory
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("A", 0, "#0000ff")]
        A = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("B", 1, "#0000ff")]
        B = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_UniversitySpecialization
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Humanities and Arts", 1, "#0000ff")]
        HumanitiesandArts = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("STEM", 0, "#0000ff")]
        STEM = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_UploadEmSATorAdvancedPlacementScores
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Advanced Placement", 1, "#0000ff")]
        AdvancedPlacement = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("EmSAT", 0, "#0000ff")]
        EmSAT = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_UtilityProvider
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("DEWA", 0, "#0000ff")]
        DEWA = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("EWE", 1, "#0000ff")]
        EWE = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("SEWA", 2, "#0000ff")]
        SEWA = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("TAQA", 3, "#0000ff")]
        TAQA = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("AADC", 4, "#0000ff")]
        AADC = 662410004,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("ADDC", 5, "#0000ff")]
        ADDC = 662410005,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_YesNoBlank
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Information Is Not Available", 2, "#FF0000")]
        InformationIsNotAvailable = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("No", 1, "#0000ff")]
        No = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Yes", 0, "#0000ff")]
        Yes = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum msft_DataState
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Default", 0)]
        Default = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Retain", 1)]
        Retain = 1,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum powerpagelanguages
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Arabic", 0, "#0000ff")]
        High = 2,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Basque - Basque", 1, "#0000ff")]
        BasqueBasque = 1069,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Bulgarian - Bulgaria", 2, "#0000ff")]
        BulgarianBulgaria = 1026,
    

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Catalan - Catalan", 3, "#0000ff")]
        CatalanCatalan = 1027,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Chinese - China", 4, "#0000ff")]
        ChineseChina = 2052,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Chinese - Hong Kong SAR", 5, "#0000ff")]
        ChineseHongKongSAR = 3076,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Chinese - Traditional", 6, "#0000ff")]
        ChineseTraditional = 1028,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Croatian - Croatia", 7, "#0000ff")]
        CroatianCroatia = 1050,
    

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Czech - Czech Republic", 8, "#0000ff")]
        CzechCzechRepublic = 1029,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Danish - Denmark", 9, "#0000ff")]
        DanishDenmark = 1030,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Dutch - Netherlands", 10, "#0000ff")]
        DutchNetherlands = 1043,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("English", 11, "#0000ff")]
        English = 1033,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Estonian - Estonia", 12, "#0000ff")]
        EstonianEstonia = 1061,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Finnish - Finland", 13, "#0000ff")]
        FinnishFinland = 1035,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("French - France", 14, "#0000ff")]
        FrenchFrance = 1036,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Galician - Spain", 15, "#0000ff")]
        GalicianSpain = 1110,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("German - Germany", 16, "#0000ff")]
        GermanGermany = 1031,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Greek - Greece", 17, "#0000ff")]
        GreekGreece = 1032,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Hebrew", 18, "#0000ff")]
        Hebrew = 1037,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Hindi - India", 19, "#0000ff")]
        HindiIndia = 1081,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Hungarian - Hungary", 20, "#0000ff")]
        HungarianHungary = 1038,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Indonesian - Indonesia", 21, "#0000ff")]
        IndonesianIndonesia = 1057,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Italian - Italy", 22, "#0000ff")]
        ItalianItaly = 1040,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Japanese - Japan", 23, "#0000ff")]
        JapaneseJapan = 1041,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Kazakh - Kazakhstan", 24, "#0000ff")]
        KazakhKazakhstan = 1087,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Korean - Korea", 25, "#0000ff")]
        KoreanKorea = 1042,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Latvian - Latvia", 26, "#0000ff")]
        LatvianLatvia = 1062,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Lithuanian - Lithuania", 27, "#0000ff")]
        LithuanianLithuania = 1063,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Malay - Malaysia", 28, "#0000ff")]
        MalayMalaysia = 1086,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Norwegian (Bokmål) - Norway", 29, "#0000ff")]
        Norwegian_BokmlNorway = 1044,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Polish - Poland", 30, "#0000ff")]
        PolishPoland = 1045,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Portuguese - Brazil", 31, "#0000ff")]
        PortugueseBrazil = 1046,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Portuguese - Portugal", 32, "#0000ff")]
        PortuguesePortugal = 2070,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Romanian - Romania", 33, "#0000ff")]
        RomanianRomania = 1048,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Russian - Russia", 34, "#0000ff")]
        RussianRussia = 1049,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Serbian (Cyrillic) - Serbia", 35, "#0000ff")]
        Serbian_CyrillicSerbia = 3098,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Serbian (Latin) - Serbia", 36, "#0000ff")]
        Serbian_LatinSerbia = 2074,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Slovak - Slovakia", 37, "#0000ff")]
        SlovakSlovakia = 1051,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Slovenian - Slovenia", 38, "#0000ff")]
        SlovenianSlovenia = 1060,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Spanish (Traditional Sort) - Spain", 39, "#0000ff")]
        Spanish_TraditionalSortSpain = 3082,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Swedish - Sweden", 40, "#0000ff")]
        SwedishSweden = 1053,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Thai - Thailand", 41, "#0000ff")]
        ThaiThailand = 1054,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Turkish - Türkiye", 42, "#0000ff")]
        TurkishTrkiye = 1055,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Ukrainian - Ukraine", 43, "#0000ff")]
        UkrainianUkraine = 1058,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Vietnamese - Vietnam", 44, "#0000ff")]
        VietnameseVietnam = 1066,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum ServiceStage
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Identify", 0)]
        Identify = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Research", 1)]
        Research = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Resolve", 2)]
        Resolve = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum SocialActivity_PostMessageType
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Private Message", 1)]
        PrivateMessage = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Public Message", 0)]
        PublicMessage = 0,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum SocialProfile_Community
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Facebook", 0, null, "Facebook item.")]
        Facebook = 1,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Other", 2, null, "Other default")]
        Other = 0,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Twitter", 1, null, "Twitter.")]
        Twitter = 2,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_contact_mocd_childeligibilityforwomenindifficulty
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not applicable", 0, "#0000ff")]
        Notapplicable = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("At least 1 child less than 4 years old", 1, "#0000ff")]
        Atleast1childlessthan4yearsold = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("At least 1 PoD child less than 21 years old", 2, "#0000ff")]
        Atleast1PoDchildlessthan21yearsold = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("At least 1 PoD child less than 25 years old if the same child is a qualified stud" +
            "ent", 3, "#0000ff")]
        Atleast1PoDchildlessthan25yearsoldifthesamechildisaqualifiedstudent = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_hexa_request_mocd_ChildEligibilityforWomeninDifficulty
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not applicable", 0, "#0000ff")]
        Notapplicable = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("At least 1 child less than 4 years old", 1, "#0000ff")]
        Atleast1childlessthan4yearsold = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("At least 1 PoD child less than 21 years old", 2, "#0000ff")]
        Atleast1PoDchildlessthan21yearsold = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("At least 1 PoD child less than 25 years old if the same child is a qualified stud" +
            "ent", 3, "#0000ff")]
        Atleast1PoDchildlessthan25yearsoldifthesamechildisaqualifiedstudent = *********,
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public enum mocd_allowancepayout_mocd_childeligibilityforwomenindifficulty
    {

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("Not applicable", 0, "#0000ff")]
        Notapplicable = 662410003,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("At least 1 child less than 4 years old", 1, "#0000ff")]
        Atleast1childlessthan4yearsold = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("At least 1 PoD child less than 21 years old", 2, "#0000ff")]
        Atleast1PoDchildlessthan21yearsold = *********,

        [System.Runtime.Serialization.EnumMemberAttribute()]
        [OptionSetMetadataAttribute("At least 1 PoD child less than 25 years old if the same child is a qualified stud" +
            "ent", 3, "#0000ff")]
        Atleast1PoDchildlessthan25yearsoldifthesamechildisaqualifiedstudent = *********,
    }
}
