﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Company
{
    public class GetCompanyDetailsRequest
    {
        public string eida { get; set; }
    }


    public class ActivitiesList
    {
        public string englishName { get; set; }
        public string arabicName { get; set; }
    }

    public class NationalCompaniesList
    {
        public string CompanyLicenseNumber { get; set; }
        public string LabourOfficeEng { get; set; }
        public string LabourOfficeArb { get; set; }
        public string CompanyNameEnglish { get; set; }
        public string CompanyNameArabic { get; set; }
        public string CompanyCode { get; set; }
        public string LicenseStartDate { get; set; }
        public string LicenseExpiryDate { get; set; }
        public string TotalEmployee { get; set; }
        public List<ActivitiesList> ActivitiesList { get; set; }
    }

    public class CompanyResponce
    {
        public string ownerNameEnglish { get; set; }
        public string ownerNameArabic { get; set; }
        public List<NationalCompaniesList> NationalCompaniesList { get; set; }
    }



}
