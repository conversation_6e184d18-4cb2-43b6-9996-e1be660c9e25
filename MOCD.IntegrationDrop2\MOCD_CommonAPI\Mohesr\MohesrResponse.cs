﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MOCD_CommonAPI.Mohesr
{
    public class MohesrResponse
    {
        public int? Count { get; set; }
        public int? PageSize { get; set; }
        public int? PageNumber { get; set; }
        public int? RemainingPages { get; set; }
        public List<MohesrStudent> List { get; set; }
    }
}
