﻿using MOCD_CRMWrapperServices.Infrastructure;
using MOCD_CRMWrapperServices.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace MOCD_CRMWrapperServices.Controllers
{
    //[RoutePrefix("API/License")]
    //private class LicenseController : ApiController
    //{
    //    private static HttpClient client;
    //    public LicenseController()
    //    {
    //        client = new HttpClient();
    //        client.Timeout = TimeSpan.FromSeconds(60);
    //    }

    //    [HttpGet, Route("GetTradeLicenses")]
    //    private async Task<IHttpActionResult> GetTradeLicenses(string licenseId)
    //    {
    //        var result = await TradeLicenseAPI.GetTradeLicense(client,licenseId);
    //        return Ok(Responses.Success(result));
    //    }
    //}
}
