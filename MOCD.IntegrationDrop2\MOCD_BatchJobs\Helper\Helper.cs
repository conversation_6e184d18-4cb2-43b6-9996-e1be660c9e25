﻿using Microsoft.Crm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MOCD_BatchJobs.Assets;

namespace MOCD_BatchJobs.Helper
{
    public static class Helper
    {
        public static KeyValuePair<string, string> FetchDataFromConfiguration(string key, IOrganizationService _gblService, ConditionOperator optr = ConditionOperator.Equal)
        {
            try
            {
                if (key == string.Empty)
                {
                    throw new Exception("Key cannot be empty");
                }
                QueryExpression queryConfig = new QueryExpression
                {
                    EntityName = hexa_hexaConfiguration.EntityLogicalName,
                    ColumnSet = new ColumnSet(hexa_hexaConfiguration.Fields.hexa_Value, hexa_hexaConfiguration.Fields.hexa_name)
                };

                queryConfig.Criteria.AddCondition(hexa_hexaConfiguration.Fields.hexa_name, optr, key);


                //Retrieving config collection and if any value is encrypted, returning it after decrypting it.
                return _gblService.RetrieveMultiple(queryConfig).Entities.Select(x => x.ToEntity<hexa_hexaConfiguration>()).Select(y => new KeyValuePair<string, string>(y.hexa_name, y.hexa_Value)).FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error in  FetchDataFromConfiguration {ex.Message}", ex);
            }

        }

        public static List<Entity> RetrieveAll(this IOrganizationService service, FetchExpression query)
        {
            var conversionRequest = new FetchXmlToQueryExpressionRequest
            {
                FetchXml = query.Query
            };

            var conversionResponse =
                (FetchXmlToQueryExpressionResponse)service.Execute(conversionRequest);

            return RetrieveAll(service, conversionResponse.Query);
        }

        public static List<Entity> RetrieveAll(this IOrganizationService service, QueryExpression query)
        {
            var result = new List<Entity>();

            var entities = service.RetrieveMultiple(query);
            result.AddRange(entities.Entities);

            var page = 2;
            while (entities.MoreRecords)
            {
                query.PageInfo = new PagingInfo
                {
                    PagingCookie = entities.PagingCookie,
                    PageNumber = page
                };

                entities = service.RetrieveMultiple(query);
                result.AddRange(entities.Entities);
                page++;
            }

            return result;
        }
    }
}
